# Deployments

## Infrastructure requirements

We have two different projects to allocate the project:

- [Natures Nectar - Dev](https://console.cloud.google.com/welcome?hl=en&inv=1&invt=AbzFyA&project=natures-nectar-dev):
  To allocate and isolate resources for development and stagging.
- [Natures Nectar - Prod](https://console.cloud.google.com/welcome?hl=en&inv=1&invt=AbzFyA&project=natures-nectar-prod):
  To allocate and isolate resources for production.

Every project will need:

- Google Cloud Run for API, BetterAuth service and NextJS static content.
- Google Cloud SQL (PostgreSQL) to store the database.
- Winter Protocol API service.

For deployments, we are using GitHub and a different GitHub environment per project.

## Creating a new project

This is a set of instructions to prepare a new CGP project to allocate environments. At the moment, as mentioned, we
have production and development, but in the future we could have staging in a separate one as well.

As a reference, next is the list of steps to prepare a new environment.

I'm going to try to use `gcloud` commands to easily reproduce it, so be sure to be logged on your account:

```bash
gcloud auth list
gcloud config <NAME_EMAIL>

# If there is project available
gcloud config set project natures-nectar-dev
```

### Prepare a new GCP project.

- Ref: https://cloud.google.com/resource-manager/docs/creating-managing-projects

```Bash
# Set required manual parameters
export PROJECT_NAME="Natures Nectar - Prod"
export GCP_REGION="europe-west1"  # Default region
export DB_INSTANCE_NAME="naturesnectar-db-prod"
export DB_VERSION="POSTGRES_16"
export DB_TIER="db-g1-small"
export DB_STORAGE_SIZE="10"

# Create the project
gcloud projects create --name="$PROJECT_NAME"

# We can list the current list of projects, to check that the project has been created, and take not of the project_id
gcloud projects list
#    PROJECT_ID                   NAME                         PROJECT_NUMBER
#    natures-nectar-dev           Natures Nectar - Dev         *********
#    natures-nectar-prod          Natures Nectar - Prod        ************
#    natures-nectar-traceability  Natures Nectar Traceability  849355455467  


# Let's get the project id filtering by name, set into an environment variable :
GCP_PROJECT_ID=$(gcloud projects list --filter="name:'$PROJECT_NAME'" --format="value(projectId)")
echo $GCP_PROJECT_ID
#    natures-nectar-prod

GCP_PROJECT_NUMBER=$(gcloud projects list --filter="name:'$PROJECT_NAME'" --format="value(projectNumber)")
echo $GCP_PROJECT_NUMBER
#    ************

# Verify project exists and is accessible
gcloud projects describe $GCP_PROJECT_ID
#    createTime: '2025-06-03T02:18:53.542762Z'
#    lifecycleState: ACTIVE
#    name: Natures Nectar - Prod
#    parent:
#      id: '************'
#      type: organization
#    projectId: natures-nectar-prod
#    projectNumber: '************'

# At the same time it is creating the project, it is creating a default VPC for the project.
gcloud compute networks list --project="$GCP_PROJECT_ID"
#    NAME     SUBNET_MODE  BGP_ROUTING_MODE  IPV4_RANGE  GATEWAY_IPV4
#    default  AUTO         REGIONAL

# We need to enable all APIs that the project will use.
gcloud services enable compute.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable sqladmin.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable secretmanager.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable artifactregistry.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable run.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable cloudscheduler.googleapis.com --project="$GCP_PROJECT_ID"
gcloud services enable cloudresourcemanager.googleapis.com --project="$GCP_PROJECT_ID"

gcloud services enable cloudrun.googleapis.com --project="$GCP_PROJECT_ID"

# After enabling compute.googleapi.com API, we will have a defaul service account.
gcloud iam service-accounts list --project="$GCP_PROJECT_ID"
#    DISPLAY NAME                            EMAIL                                               DISABLED
#    Compute Engine default service account  <EMAIL>  False


# Access to secrets for service account.
gcloud projects add-iam-policy-binding $GCP_PROJECT_ID \
  --member="serviceAccount:$<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $GCP_PROJECT_ID \
  --member="serviceAccount:$<EMAIL>" \
  --role="roles/run.admin"


# Configure the default region. We are going to use Europe because proximity.
gcloud config set compute/region europe-west1 --project="$GCP_PROJECT_ID"
#  Updated property [compute/region].


# We need an Google Cloud SQL instance to allocate the databases.
GCP_DB_PASSWORD_SECRET_NAME=DB_PASSWORD_$(echo "$DB_INSTANCE_NAME" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
export DB_PASSWORD=$(gcloud secrets versions access latest --secret=DB_PASSWORD --project=$GCP_PROJECT_ID) 
gcloud sql instances create $DB_INSTANCE_NAME \
  --project="$GCP_PROJECT_ID" \
  --region=$GCP_REGION \
  --edition=ENTERPRISE \
  --database-version=$DB_VERSION \
  --availability-type=REGIONAL \
  --tier=$DB_TIER \
  --deletion-protection \
  --replica-type=FAILOVER \
  --storage-auto-increase \
  --storage-type=SSD \
  --storage-size=$DB_STORAGE_SIZE \
  --root-password=$DB_PASSWORD \
  --backup \
  --database-flags=max_connections=100 \
  --assign-ip \
  --authorized-networks=*************/32,*********/32,*************/32 \
  --backup-start-time=00:00


# Config the GitHub Environment.
# To generate a service key:
gcloud iam service-accounts keys create \
  --project="$GCP_PROJECT_ID" \
  ./$GCP_PROJECT_ID-account-key.json \
  --iam-account=$<EMAIL>
cat $GCP_PROJECT_ID-account-key.json | jq -c


# Create a artifact repository
gcloud artifacts repositories create palmyra-pro-images \
  --project="$GCP_PROJECT_ID" \
  --repository-format=docker \
  --location=$GCP_REGION \
  --description="Docker images for Palmyra Pro"


```


### Cloud Provider

- Google Cloud Platform (GCP)
- Cloud Run for containerized application deployment
- Cloud SQL for database (PostgreSQL)

### Hardware Requirements

- Minimal Cloud Run instance: 1 vCPU, 256MB RAM
- Recommended: 2 vCPU, 512MB RAM
- Storage: Minimum 10GB for application and data

### Network & Security

- HTTPS endpoints required
- VPC with private subnet recommended
- Cloud NAT for outbound traffic
- Load balancer with SSL termination

### Required Credentials

- GCP Service Account with following roles:
    - Cloud Run Admin
    - Cloud SQL Client
    - Storage Object Viewer
- GitHub Actions Workload Identity Federation

Deployments are in early stage but follow the infrastructure requirements above.

References:

- [Authentication in GCP from GitHub Actions](https://github.com/google-github-actions/auth)
- [Deploy from GitHub Actions](https://cloud.google.com/blog/products/devops-sre/deploy-to-cloud-run-with-github-actions/)

After creating the GCP Key, it's necessary to clean it for GitHub secrets.

```shell
cat natures-nectar-dev-85c0f6338ae6.json | jq -c > natures-nectar-dev-85c0f6338ae6-oneline.json
```