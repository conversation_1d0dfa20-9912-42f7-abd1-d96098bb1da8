# Items Persistence Layer

## Entity Relationship Diagram (ERD)

![items-low-level-design.png](items-low-level-design.png)

## Context 

Now we have a clear view of what we need for the component/module that will store the data in a way that we can:

- Keep a log of all changes.
- Isolate data per tenant. Every sandbox will be called a "Tenant Space".
- Allow tenants to customize and create their own models via the "Tenant Item Definitions Repository".
- Integrate with tools via SQL for reporting, dashboards, analysis, etc.
- Allow tenants to query the database directly.

Technically, the first implementation will:

- Use PostgreSQL as the storage system.
- Handle all interaction with the DB from the backend via a common library.
- Handle all interaction with the persistence layer from the frontend via an API.
- Use a single database per deployment, but:

    - Each tenant will be isolated in its own schema. This schema represents the tenant space.
    - SaaS admin/configuration data will be stored in a separate, dedicated schema.

We will have the following components:

## Tenant "Item Definition Repository"

This is a repository that contains the definitions of all Items available **in the tenant spaces**.

Each definition will have:

- A set of metadata useful for Palmyra Pro capabilities, e.g.:
    - Enable Winter Protocol traceability.
    - Enable data versioning or logging.
- A set of per-tenant metadata to store custom information. ???
- A schema definition in JSON Schema format, specifying field definitions, constraints, and relationships.

Data in this table is always immutable, so it has a PK with the UUID representing the definition and a version.

This data is stored in a table called `item_log_definitions`, and the schema will contain:

- `id` (UUID) + `version`
- `name`

    - Unique per tenant space.
    - Must be a valid table name and follow naming conventions, e.g., `farmers` or `administrative_area`.
    - If imported from a [global definition](#global-item-definition-repository), the name must be preserved. Otherwise,
      it should use the prefix `tenant_`.
- `schema` – the most important field, containing the item definition as a json schema field.
- `creation_ts` – creation timestamp. Since this table is immutable, no other timestamps are needed.
- `deleted` – boolean flag
- `description` – markdown text describing the item.
- `is_latest` – flag to facilitate creating a view of the latest version.
- `global_definition_[id,version]` – optional reference to the global Palmyra Pro item definition.

We also have a view called `item_definitions`, which will be a projection of `item_log_definitions`, showing only
the latest version of each definition.

## Tenant "Item Logs"

This is where tenant data will be stored and versioned.

For every definition in the "Tenant Item Definition Repository", we will have one table called
`item_log_<ItemDefinitionName>` that contains all the data related to that definition.

Data stored in these tables may be immutable or not, depending on the Definition configuration.

Each log table will contain:

- `id` (UUID) + `version`
- `data` – JSONB field with data, validated against its item definition.
- `definition_[id,version]` – the item definition used for this version.
- `is_latest` – flag to help create a view of the latest version.

## Tenant "Item Views"

We will also have a view called `<ItemDefinitionName>`, which is a projection of the corresponding
`log_<ItemDefinitionName>` showing only the latest version of each item.

To allow it on an easy and performance way, we use the flag `is_latest` from the log table.


## Global "Item Definition Repository"

This is a repository that contains the definitions of all Items available **in the Palmyra Pro spaces**.

These definitions will serve as templates for tenants. They also define constraints that tenant definitions must follow
to be compatible with Palmyra Pro’s predefined workflows. For example, honey processing flows require definitions like
Farmers, Honeybees, and Administrative Areas in the tenant space, with fields like `name` required.

All definitions are categorized, allowing tenants to select groups of item definitions and workflows. For example, the "
Honey Producers" category may include "farmers", "honeybees", "buckets", etc.

# Schema Definition

As described earlier, every item is versioned and linked to a schema. Each schema will also be versioned.

Schemas will be defined using [JSON Schema 2020-12](https://json-schema.org/draft/2020-12). These schemas will be used
to validate the content every time an item is created or updated. To achieve this, all mutations will be proxied through
an SDK/library in the backend and, as usual, via API in the frontend.
