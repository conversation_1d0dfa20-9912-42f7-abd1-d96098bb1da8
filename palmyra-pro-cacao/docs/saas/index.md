# Palmyra Pro - Technical Vision Document - MVP2

## Introduction

Palmyra Pro is a SaaS platform designed to orchestrate and trace the transformation of versioned data objects (called
Items) across configurable workflows. It supports flexible schema validation, tenant-specific customization,
traceability, and external integration.

This will allow us to create and deploy new sectors and onboard new tenants (customers) in no time, without requiring
intervention from the engineering team.

This document captures the shared vocabulary, architecture, and technical direction agreed upon so far.

## Meeting & Demo Recordings

- [📹 Recording from 20 May 2025](https://drive.google.com/file/d/1ivgUa-8apBBqTlnhNDNrqeSvryicZDsX/view)

## HLD Diagram

![high-level-design.png](high-level-design.png)

## Domains

We are going to split Palmyra Pro into the following domains:

1. Item Persistence Layer
    - [Documentation](./item-storage.md)
    - Item Storage
        - Status: design done
        - Implementation: custom SDK
        - One Postgres schema per tenant
        - One Postgres global schema
        - One table per every item definition
        - Versioned immutable records (id + version) + `is_latest` for current state
    - Schema Management
        - Status: design done
        - Implementation: homemade schema registry stored in Postgresql
        - Custom definitions repository per tenant
        - Global definitions templates per categories
        - One definition (so json schema) per Item type
2. Workflow Execution
    - [Documentation](./workflows.md)
    - Status: design in progress
    - Engine: Temporal???
    - Steps: Activities (Go)
    - Signal-based UI input handling
3. Lineage
    - Status: TBC
4. Access Control
    - Status: TBC
    - Tenant access to categories???
    - User permissions to Items and Steps
    - Per-tenant DB user for read-only analisys and exports
5. UI Integration
    - Status: TBC
    - React-based Step UIs
    - Dynamic forms driven by schema
6. EPCIS
    - Status: TBC
    - From Items and Workflow definitions, generate EPCIS standard records.
7. Data Exports
    - Status: TBC
    - Allow export data for Data Analysts, Data Scientist, etc.
    - Integration with tools to create data marts and handle sensitive information: obfuscation, anonymization, encryption, masking, etc.


## Glossary / Vocabulary

| Term                       | Definition                                                                                                                                                       |
|----------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **ItemDefinition**         | A template defining the expected fields (e.g., honeybee or bucket) and behaviors (e.g., enable winter protocol integration, immutable, enable logging) of Items. |
| **Item**                   | A versioned data object defined by `ItemDefinition`, flowing through Steps.                                                                                      |
| **ItemData**               | JSON structure representing the fields of an Item, validated by the schema specified by an `ItemDefinition` from the Schema Registry.                            |
| **ItemSchema**             | A stored JSON Schema set for a given `ItemDefinition`.                                                                                                           |
| **Versioned Item**         | An immutable snapshot of an Item at a given Step in a Workflow.                                                                                                  |
| **Schema Registry**        | The service/module that stores ItemDefinitions. We will have both global and per-tenant versions.                                                                |
| **Tenant Schema Override** | Custom schema extensions or restrictions per tenant.                                                                                                             |
| **Workflow Log**           | Structured or unstructured annotations created during Step execution.                                                                                            |
| **Workflow**               | A defined, traceable process composed of ordered Steps. It consumes and produces `Items`. It's managed and executed via a workflow engine.                       |
| **Step**                   | A unit of work in a Workflow. It may require user input, call external services, or run business logic.                                                          |
| **StepInputItem**          | An Item used or consumed by a Step.                                                                                                                              |
| **StepOutputItem**         | An Item produced by a Step. Represents a new version.                                                                                                            |
| **StepParameter**          | UI-provided or system-provided values used to execute a Step.                                                                                                    |
| **Step UI**                | React component rendered in the UI to collect Step parameters.                                                                                                   |
| **Lineage**                | Information about Item versions and their transformation history.                                                                                                |
| **Journal**                | Lineage representation of an `Item`. Could be represented as a tree.                                                                                             |
| **Contract?????**          | Defines which `ItemDefinitions` and `Workflows` are available to a tenant.                                                                                       |

## Roadmap

This will be the transition from Natures Nectar to the Palmyra Pro SaaS.

The target for MVP2 will be:

- **No self-service** definition of new sectors.
- Migrate Natures Nectar to the new SaaS solution to prove the model.
- Onboard a new commodity
- Design a new category / commodity.

```mermaid
gantt
    title Pamyra Pro SaaS roadmap
    dateFormat YYYY-MM-DD
    excludes weekends
    section Spikes
        Spike Micro Frontend: spike_mf, after pl_analisys, 3d
        Spike monorepo: spike_mr, after pl_analisys, 3d
    section Persistence Layer and schema (PL)
        Persistence Layer analysis: pl_analisys, 2025-05-05, 10d
        SDK implementation: pl_sdk, after pl_analisys, 10d
        Schema Repository: ps_schema_repo, after pl_analisys, 5d
    section Workflow
        Analysis: wf_analisys, after pl_analisys, 5d
        Implementation: after wf_analisys, 15d
    section Winter Protocol
        Analysis: wp_analisys, after pl_analisys, 5d
        Step Implementation: after wf_analisys, 15d
    section Natures Nectar Migration
        Persistence Layer: 2025-05-22, 10d
        Workflow: 2025-05-29, 10d
        Winter protocol: 2025-06-2, 10d
```

---

## Appendix / ADR Index

- [ADR-005: Schema Definition for Flexible JSONB Data Validation](https://docs.google.com/document/d/1qtN-wlSqiuOi579YRqE39nz6Uh_IoRM7LgUlsYDgc1w)
- ADR-XXX: Use Temporal for workflow orchestration
- ADR-XXX: PostgreSQL schema-per-tenant model
- ADR-XXX: Store item metadata as JSONB with version tracking
- ADR-XXX: Dynamic schema registry + tenant overrides

---

## TODO and notes

- We need to decide whether to allow disabling versioning in `item_<ItemDefinitionName>`. It may be better to have all
  data immutable and versioned in the first MVP.

- `itemDefinitionIDs` outside of the tenant scope (like in Winter Protocol payloads) should also represent the host to
allow custom deployments. Example: `zg:nnz:bucket:0.1` where `zg` is the origin/namespace (representing, for example, the deployment location) and `nnz` the tenant
