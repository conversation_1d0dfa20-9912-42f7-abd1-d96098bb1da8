# Monorepo management.

## Steps to create the repo

Next the list of steps used to create the current monorepo. 

```shell

## Create the turbo project
npm install turbo --global # If not installed before.

## Outside the porject
npx create-turbo@latest palmyra-pro-next14-react18-tailwind3-shadcn \
    --skip-install \
    -m npm \
    --example kitchen-sink


cd palmyra-pro-next14-react18-tailwind3-shadcn/apps

## Create nextjs: https://nextjs.org/docs/app/api-reference/cli/create-next-app
## Default options and yes to alias.
npx create-next-app@14 frontend \
    --app \
    --src-dir "src/" \
    --import-alias "@/*" \
    --typescript \
    --eslint \
    --skip-install \
    --use-npm \
    --tailwind

cd frontend

## Integrate Shadcn
npx shadcn@latest init -y

## Add component as example into `src/components/ui/button.tsx`
## So we will need our own components in <ROOT>/src/components/ui/.....
npx shadcn@latest add button

## Add a button for testing into `src/app/page.tsx`:
## import { Button } from "@/components/ui/button"
## ....
## <Button>Click me</Button>


# - frontend at  http://localhost:9080/
# - api at  http://localhost:4001/

cd ../..
npm install
npm run dev

```


## Refs:
- Add new packages: https://turbo.build/repo/docs/crafting-your-repository/creating-an-internal-package
- 
