# GLOBALS
ENV_SHORT_NAME: local

# DB
DB_NAME: "palmira_pro_db"
DB_HOST: "localhost"
DB_PORT: 5432
DB_USER: "postgres"
DB_PASSWORD: "mysecretpassword"
DB_SSL_MODE: "disable" # In prod: "require" https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-SSLMODE-STATEMENTS

# API
METABASE_SITE_URL: "https://zengate-global.metabaseapp.com"
METABASE_SECRET_KEY: "bdc8xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx02"
ALLOWED_ORIGIN: "http://localhost:9080"

# AUTH
FIREBASE_AUTH_SECRET: 1wexxxxxxxxxxxxxxxxxxxxxMiy
FIREBASE_ADMIN_KEY_PATH=./serviceAccount.json

# Front end
NEXT_PUBLIC_BACKEND_URL: "http://localhost:4001/"
