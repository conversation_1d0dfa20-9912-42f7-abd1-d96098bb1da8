package db

import (
	"fmt"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/extra/bundebug"
	"log"
)

type Config struct {
	Name, Host, Port, User, Password, SSLMode string
	Debug                                     bool
	MaxOpenConn                               int
	MaxIdleConns                              int
}

func NewDB(c *Config) (*bun.DB, error) {

	dbConnection := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.Name, c.SSLMode,
	)

	config, err := pgx.ParseConfig(dbConnection)
	if err != nil {
		return nil, err
	}

	sqldb := stdlib.OpenDB(*config)
	sqldb.SetMaxOpenConns(c.<PERSON>)
	sqldb.SetMaxIdleConns(c.<PERSON>)

	// Ensure the connection is working
	if err := sqldb.Ping(); err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	db := bun.NewDB(sqldb, pgdialect.New())
	if c.Debug {
		db.AddQueryHook(bundebug.NewQueryHook(bundebug.WithVerbose(true)))
	}

	return db, nil
}
