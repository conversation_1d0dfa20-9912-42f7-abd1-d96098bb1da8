package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserRepo(t *testing.T) {
	db := setupTestConnection()
	repo := NewUserRepo(db)
	ctx := context.Background()

	users, err := repo.Search(ctx, nil, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 15, len(users))

	users, err = repo.Search(ctx, nil, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 2, len(users))

	users, err = repo.Search(ctx, nil, 0, 100)
	require.NoError(t, err, "Failed getting users search.")
	assert.Equal(t, 3, len(users))
}
