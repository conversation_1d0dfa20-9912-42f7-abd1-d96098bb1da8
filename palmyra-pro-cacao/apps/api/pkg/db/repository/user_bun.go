package repository

import (
	"context"
	"database/sql"
	"errors"
	"palmyra-pro-api/pkg/db/models"

	"github.com/uptrace/bun"
)

type userRepo struct {
	db *bun.DB
}

func NewUserRepo(db *bun.DB) UserRepo {
	return &userRepo{
		db: db,
	}
}

func (r *userRepo) Search(
	ctx context.Context,
	fuzzyFilter *string,
	offset,
	limit int,
) ([]*models.User, error) {

	var users []*models.User
	query := r.db.NewSelect().
		Model(&users).
		Order("first_name asc")

	// FIXME: Use a real fuzzy search solution.
	if fuzzyFilter != nil {
		query = query.WhereOr(`"firstName" || ' ' || "lastName" ILIKE ? OR farmer_quick_search.id = ?`, "%"+*fuzzyFilter+"%", *fuzzyFilter)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Scan(ctx)

	if err != nil {
		return nil, err
	}

	return users, nil
}

func (r *userRepo) GetByExternalUID(ctx context.Context, externalUID string) (*models.User, error) {
	var user models.User
	err := r.db.NewSelect().
		Model(&user).
		Where("external_uid = ?", externalUID).
		Limit(1).
		Scan(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func (r *userRepo) CreateUser(ctx context.Context, user *models.User) error {
	_, err := r.db.NewInsert().Model(user).Exec(ctx)
	return err
}