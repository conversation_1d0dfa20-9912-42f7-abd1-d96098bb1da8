package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"github.com/uptrace/bun"
)

type farmerRepo struct {
	db *bun.DB
}

func NewFarmerRepo(db *bun.DB) FarmerRepo {
	return &farmerRepo{
		db: db,
	}
}

func (r *farmerRepo) CreateFarmer(ctx context.Context, farmer *models.Farmer) error {
	_, err := r.db.NewInsert().Model(farmer).Exec(ctx)
	return err
}

func (r *farmerRepo) GetFarmerByID(ctx context.Context, id string) (*models.Farmer, error) {
	farmer := new(models.Farmer)
	err := r.db.NewSelect().Model(farmer).Where("farmer.id = ?", id).Scan(ctx)
	return farmer, err
}

func (r *farmerRepo) UpdateFarmer(ctx context.Context, farmer *models.Farmer) error {
	_, err := r.db.NewUpdate().Model(farmer).Where("id = ?", farmer.ID).Exec(ctx)
	return err
}

func (r *farmerRepo) DeleteFarmer(ctx context.Context, id string) error {
	_, err := r.db.NewUpdate().
		Model((*models.Farmer)(nil)).
		Set("is_deleted = ?", true).
		Set(`"updated_at" = ?`, time.Now()).
		Where("id = ?", id).
		Exec(ctx)
	return err
}

func (r *farmerRepo) AllFarmersOffline(ctx context.Context) ([]*models.Farmer, error) {
	var farmers []*models.Farmer

	query := r.db.NewSelect().
		Model(&farmers)

	err := query.Scan(ctx)

	return farmers, err
}

func (r *farmerRepo) SearchFarmers(ctx context.Context, fuzzyFilter string, offset, limit int) ([]*models.Farmer, error) {
	var err error
	var farmers []*models.Farmer

	query := r.db.NewSelect().
		Model(&farmers)

	// FIXME: Use a real fuzzy search solution.
	if len(fuzzyFilter) > 0 {
		query = query.WhereOr(`nrc ILIKE ? OR first_name || ' ' || last_name ILIKE ? OR farmer.id = ?`, "%"+fuzzyFilter+"%", "%"+fuzzyFilter+"%", fuzzyFilter)
	}

	query = query.Where("is_deleted = ?", false)
	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	err = query.Scan(ctx, &farmers)

	return farmers, err
}
