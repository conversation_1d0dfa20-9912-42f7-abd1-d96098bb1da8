package repository

import (
	"context"
	"palmyra-pro-api/pkg/db/models"
)

type FarmerRepo interface {
	CreateFarmer(ctx context.Context, farmer *models.Farmer) error
	GetFarmerByID(ctx context.Context, id string) (*models.Farmer, error)
	UpdateFarmer(ctx context.Context, farmer *models.Farmer) error
	DeleteFarmer(ctx context.Context, id string) error
	AllFarmersOffline(ctx context.Context) ([]*models.Farmer, error)
	SearchFarmers(ctx context.Context, fuzzyFilter string, offset, limit int) ([]*models.Farmer, error)
}
