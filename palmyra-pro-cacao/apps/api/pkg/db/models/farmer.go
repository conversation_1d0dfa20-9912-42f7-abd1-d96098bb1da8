package models

import (
	"time"

	"github.com/uptrace/bun"
)

type Farmer struct {
	bun.BaseModel `bun:"table:farmer"`

	ID                    string  `bun:"id,pk"`
	FirstName             string  `bun:"first_name,nullzero"`
	LastName              string  `bun:"last_name,nullzero"`
	Gender                string  `bun:"gender,nullzero"`
	Phone                 string  `bun:"phone,nullzero"`
	NRC                   string  `bun:"nrc,nullzero"`
	MaritalStatus         string  `bun:"marital_status,nullzero"`
	Dob                   *string `bun:"dob,nullzero"`
	HouseholdSize         string  `bun:"household_size,nullzero"`
	EstimatedAnnualIncome float32 `bun:"estimated_annual_income,nullzero"`
	SourceOfIncome        string  `bun:"source_of_income,nullzero"`

	CreatedAt time.Time `bun:"created_at,default:current_timestamp"`
	UpdatedAt time.Time `bun:"updated_at,default:current_timestamp"`
	IsDeleted bool      `bun:"is_deleted,default:false"`
}
