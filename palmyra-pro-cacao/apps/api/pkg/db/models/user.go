package models

import (
	"time"

	"github.com/uptrace/bun"
)

type User struct {
	bun.BaseModel `bun:"table:user"`

	ID             string    `bun:"id,pk"`
	ExternalUID    string    `bun:"external_uid,unique,nullzero"`
	FirstName      string    `bun:"first_name,nullzero"`
	LastName       string    `bun:"last_name,nullzero"`
	Email          string    `bun:"email,notnull,unique"`
	UpdatedAt      time.Time `bun:"updated_at,default:current_timestamp"`
	CreatedAt      time.Time `bun:"created_at,default:current_timestamp"`
}
