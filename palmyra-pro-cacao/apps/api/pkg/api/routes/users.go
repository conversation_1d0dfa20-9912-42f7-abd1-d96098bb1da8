package routes

import (
	"context"
	"fmt"
	"palmyra-pro-api/pkg/api"
	"palmyra-pro-api/pkg/db/models"
	"time"

	"palmyra-pro-api/pkg/utils"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/auth"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"google.golang.org/api/option"
)

var firebaseAuth *auth.Client

func InitFirebase() error {
	jwtKeyUrl := utils.GetEnvOrPanic("FIREBASE_ADMIN_KEY_PATH")
	opt := option.WithCredentialsFile(jwtKeyUrl)
	app, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		return err
	}

	firebaseAuth, err = app.Auth(context.Background())
	return err
}

func strPtr(s string) *string {
	return &s
}

// Search users
// (GET /users)
func (s Server) GetUsers(ctx context.Context, request api.GetUsersRequestObject) (api.GetUsersResponseObject, error) {

	pageSize := cast.ToInt(request.Params.PageSize)

	users, err := s.UserRepo.Search(
		ctx,
		request.Params.Query,
		cast.ToInt(request.Params.Page)*pageSize,
		pageSize,
	)

	if err != nil {
		return nil, err
	}

	var results []api.User
	for _, u := range users {
		results = append(results, api.User{
			Id:   u.ID,
		})
	}

	response := api.UserList{
		Page:       nil,
		PageSize:   nil,
		Results:    &results,
		Total:      nil,
		TotalPages: nil,
	}

	return api.GetUsers200JSONResponse(response), nil
}

// (GET /users/{external_uid})
func (s Server) GetByExternalUID(
	ctx context.Context, 
	request api.GetByExternalUIDRequestObject,
	) (api.GetByExternalUIDResponseObject, error) {
	fmt.Println("call")
	user, err := s.UserRepo.GetByExternalUID(
		ctx, 
		request.ExternalUid,
	)

    if err != nil {
        return nil, err
    }

	if user == nil {
		return api.GetByExternalUID404JSONResponse{
			Code:    strPtr("NOT_FOUND"),
			Message: strPtr("User not found"),
		}, nil
	}

    return api.GetByExternalUID200JSONResponse{
		Id:          user.ID,
		ExternalUID: &user.ExternalUID,
		FirstName:   &user.FirstName,
		LastName:    &user.LastName,
		Email:       &user.Email,
	  }, nil
}

// Create a new user
// (POST /user)
func (s Server) CreateUser(ctx context.Context, request api.CreateUserRequestObject) (api.CreateUserResponseObject, error) {
	params := (&auth.UserToCreate{}).
		Email(request.Body.Email).
		Password(request.Body.Password)
	
	u, err := firebaseAuth.CreateUser(ctx, params)
	if err != nil {
		return nil, err
	}

	id := uuid.New().String()
	now := time.Now()

	user := models.User{
		ID:          id,
		ExternalUID: u.UID,
		FirstName:   request.Body.FirstName,
		LastName:    request.Body.LastName,
		Email:       request.Body.Email,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err = s.UserRepo.CreateUser(
		ctx, 
		&user,
	)
	if err != nil {
		return nil, err
	}

	return api.CreateUser201JSONResponse{
		Id: &id,
	}, nil
}