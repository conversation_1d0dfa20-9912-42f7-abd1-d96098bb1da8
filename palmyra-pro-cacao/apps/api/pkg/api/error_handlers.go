package api

import (
	"encoding/json"
	"net/http"
	"palmyra-pro-api/pkg/utils"
)

func RequestErrorHandlerFunc(w http.ResponseWriter, r *http.Request, err error) {
	sendResponse(w, "UNHANDLED_REQUEST_ERROR", err)
}

func ResponseErrorHandlerFunc(w http.ResponseWriter, r *http.Request, err error) {
	sendResponse(w, "UNHANDLED_RESPONSE_ERROR", err)
}

func NewErrorHandlerOptions() StrictHTTPServerOptions {
	return StrictHTTPServerOptions{
		RequestErrorHandlerFunc:  RequestErrorHandlerFunc,
		ResponseErrorHandlerFunc: ResponseErrorHandlerFunc,
	}
}

func sendResponse(w http.ResponseWriter, errorCode string, originalError error) {
	utils.NewLogEntryf(errorCode).
		WithComponent("api").
		WithSubComponent(errorCode).
		LogWithError(originalError)

	errorMessage := Error{
		Code:    errorCode,
		Message: originalError.Error(),
	}

	errorResponse, err2 := json.Marshal(errorMessage)
	if err2 != nil {
		http.Error(w, originalError.Error(), http.StatusInternalServerError)
	} else {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		w.Write(errorResponse)
	}
}
