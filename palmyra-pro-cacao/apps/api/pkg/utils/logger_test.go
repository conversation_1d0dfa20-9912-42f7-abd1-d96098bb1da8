package utils

import (
	"encoding/json"
	"fmt"
	"testing"
)

type WrappedErrorExample struct {
	OtherField   string `json:"otherField"`
	Msg          string `json:"msg"`
	WrappedError error  `json:"error"`
}

func (p *WrappedErrorExample) Error() string {
	out, err := json.Marshal(p)
	if err != nil {
		return fmt.Sprintf(
			"Error marshalling proxy error: [%s]. The error content in ugly format is msg: [%s] error: %s",
			p.<PERSON>, p.Wrapped<PERSON>rror,
			err.Error(),
		)
	}
	return string(out)
}

func (p *WrappedErrorExample) MarshalJSON() ([]byte, error) {
	type WrappedErrorExampleAlias WrappedErrorExample

	return json.Marshal(&struct {
		*WrappedErrorExampleAlias
		ErrorMsg string `json:"error"`
	}{
		WrappedErrorExampleAlias: (*WrappedErrorExampleAlias)(p),
		ErrorMsg:                 p.WrappedError.Error(),
	})
}

func TestNewLogEntryJson(t *testing.T) {
	NewLogEntryJson(fmt.Errorf("error testing").Error()).WithComponent("error_testing").LogInfo()
	NewLogEntryJson("xxxxxx").WithComponent("error_testing").LogInfo()

	var err error

	err = &WrappedErrorExample{
		OtherField:   "Whatever here",
		Msg:          "Message",
		WrappedError: fmt.Errorf("error testing"),
	}

	NewLogEntryJson(err).WithComponent("error_testing").LogInfo()
}
