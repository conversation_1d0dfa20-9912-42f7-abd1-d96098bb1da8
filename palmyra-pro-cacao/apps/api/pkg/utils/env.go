package utils

import (
	"log"
	"os"
)

func GetEnvOrPanic(name string) string {
	value, found := os.LookupEnv(name)
	if !found {
		log.Fatalf("%s environment variable not set", name)
	}

	return value
}

func GetEnvOrDefault(name, defValue string) string {
	value, found := os.LookupEnv(name)
	if !found {
		log.Printf("%s environment variable not set. Using fallback value [%s]\n", name, defValue)
		return defValue
	}

	return value
}
