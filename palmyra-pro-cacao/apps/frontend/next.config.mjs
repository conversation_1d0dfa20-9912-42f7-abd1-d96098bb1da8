import serwistNext from "@serwist/next"

/** @type {(phase: string, defaultConfig: import("next").NextConfig) => Promise<import("next").NextConfig>} */
export default async (phase) => {
  /** @type {import("next").NextConfig} */
  const nextConfig = {
    output: "export",
    distDir: "dist",
    reactStrictMode: true,
    swcMinify: true,
    trailingSlash: true,
    eslint: {
      ignoreDuringBuilds: process.env.CI === 'true' || process.env.SKIP_TYPE_CHECK === 'true',
    },
    typescript: {
      ignoreBuildErrors: process.env.CI === 'true' || process.env.SKIP_TYPE_CHECK === 'true',
    },
  }

  const withSerwist = serwistNext({
    swSrc: "src/app/sw.ts",
    swDest: "public/sw.js",
    cacheOnNavigation: false,
    additionalPrecacheEntries: [
      { url: "/offline", revision: "v1" },
      { url: "/manifest.json", revision: "v1" },
      { url: "/farmers", revision: "v1" },
      { url: "/farmers/create", revision: "v1" },
      { url: "/farmers/farmer", revision: "v1" },
      { url: "/farmers/farmer/create", revision: "v1" },
      { url: "/farmers/farmer/edit", revision: "v1" },
    ],
  })

  return withSerwist(nextConfig)
}
