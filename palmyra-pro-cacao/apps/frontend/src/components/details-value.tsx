import { FC } from "react"
import { cn } from "@/lib/utils"

interface Props {
  label: string
  value: string | number | boolean | undefined
  className?: string
}

export const DetailsValue: FC<Props> = ({ label, value, className }) => (
  <div className={cn("space-y-1", className)}>
    <p className="text-xs uppercase text-muted-foreground">{label}</p>
    <p className="text-sm">
      {typeof value === "boolean" ? (value ? "Yes" : "No") : value || "-"}
    </p>
  </div>
)
