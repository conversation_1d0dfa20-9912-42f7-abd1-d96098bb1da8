"use client"

import { <PERSON> } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Loader } from "lucide-react"

interface NextButtonProps {
  isLast: boolean
  disabled?: boolean
  formPath?: string
  isLoading?: boolean
  handleNext: () => void
}

export const NextButton: FC<NextButtonProps> = ({
  isLast,
  disabled,
  formPath,
  isLoading = false,
  handleNext,
}) => {
  const pathname = usePathname()

  if (formPath) {
    const handleNewForm = (e: React.MouseEvent) => {
      if (pathname === window.location.pathname) {
        e.preventDefault()
        window.location.reload()
      }
    }
    return (
      <div className="w-full flex gap-4">
        <Button
          asChild
          variant="secondary"
          className="w-full"
          disabled={isLoading || disabled}
        >
          <Link href={formPath}>
            {isLoading ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "See form"
            )}
          </Link>
        </Button>
        <Button asChild className="w-full" disabled={isLoading || disabled}>
          <Link href={pathname} onClick={handleNewForm}>
            {isLoading ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "New form"
            )}
          </Link>
        </Button>
      </div>
    )
  }

  if (isLast) {
    return (
      <Button
        className="w-full mt-4"
        disabled={isLoading || disabled}
        type="submit"
      >
        {isLoading ? (
          <>
            <Loader className="mr-2 h-4 w-4 animate-spin" />
            Loading...
          </>
        ) : (
          "Finalize"
        )}
      </Button>
    )
  }

  return (
    <Button
      className="w-full mt-4"
      disabled={isLoading || disabled}
      onClick={handleNext}
      type="button"
    >
      {isLoading ? (
        <>
          <Loader className="mr-2 h-4 w-4 animate-spin" />
          Loading...
        </>
      ) : (
        "Next"
      )}
    </Button>
  )
}
