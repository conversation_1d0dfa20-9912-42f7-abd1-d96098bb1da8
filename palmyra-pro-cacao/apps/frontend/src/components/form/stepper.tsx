"use client"

import { ArrowLef<PERSON>, Check } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"

export interface Step {
  id: number
  label: string
  status: "completed" | "current" | "upcoming"
}

interface StepperProps {
  steps: Step[]
  progress: number
  backDisabled: boolean
  onBack?: () => void
}

export function Stepper({
  steps,
  progress,
  backDisabled,
  onBack,
}: StepperProps) {
  return (
    <div className="w-full mb-5 flex flex-col">
      <div className="flex items-center gap-2 mb-4">
        {onBack && (
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="h-8 w-8"
            disabled={backDisabled}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="sr-only">Go back</span>
          </Button>
        )}
        <Progress value={progress} className="flex-1" />
      </div>

      {/* Mobile View */}
      <div className="flex flex-col gap-4 sm:hidden px-1 mb-4">
        {steps.map((step) => (
          <div
            key={step.id}
            className={cn("flex items-center", {
              "opacity-100": step.status !== "upcoming",
              "opacity-60": step.status === "upcoming",
            })}
          >
            <div
              className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full text-white font-medium shrink-0",
                {
                  "bg-[#065f46]": step.status === "completed",
                  "bg-primary": step.status === "current",
                  "bg-[#777e90]": step.status === "upcoming",
                }
              )}
            >
              {step.status === "completed" ? (
                <Check className="w-5 h-5" />
              ) : (
                <span>{step.id}</span>
              )}
            </div>
            <span
              className={cn("ml-3 text-sm font-medium", {
                "text-[#065f46]": step.status === "completed",
                "text-primary": step.status === "current",
                "text-[#777e90]": step.status === "upcoming",
              })}
            >
              {step.label}
            </span>
          </div>
        ))}
      </div>

      {/* Desktop View */}
      <div className="hidden sm:flex items-center justify-between mb-5">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className="flex items-center flex-1 last:flex-none"
          >
            <div className="flex items-center gap-3 shrink-0">
              <div
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full text-white font-medium shrink-0",
                  {
                    "bg-[#065f46]": step.status === "completed",
                    "bg-primary": step.status === "current",
                    "bg-[#777e90]": step.status === "upcoming",
                  }
                )}
              >
                {step.status === "completed" ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span>{step.id}</span>
                )}
              </div>
              <span
                className={cn("text-sm font-medium whitespace-nowrap", {
                  "text-[#065f46]": step.status === "completed",
                  "text-primary": step.status === "current",
                  "text-[#777e90]": step.status === "upcoming",
                })}
              >
                {step.label}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className="h-[1px] bg-[#e6e8ec] w-full mx-4" />
            )}
          </div>
        ))}
      </div>

      <Separator />
    </div>
  )
}
