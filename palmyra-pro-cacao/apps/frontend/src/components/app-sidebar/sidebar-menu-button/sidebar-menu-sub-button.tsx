"use client"

import { FC } from "react"
import { usePathname } from "next/navigation"
import { useMemo } from "react"
import { SidebarMenuSubButton as ShadcnSidebarMenuSubButton } from "@/components/ui/sidebar"

interface SidebarLinkProps {
  href: string
  children: React.ReactNode
  className?: string
}

const SidebarMenuSubButton: FC<SidebarLinkProps> = ({ href, children }) => {
  const pathname = usePathname()
  const isActive = useMemo(() => {
    const normalizedHref = href.replace(/\/$/, "") // Remove trailing slash
    const normalizedPathname = pathname.replace(/\/$/, "")

    return normalizedHref === normalizedPathname
  }, [href, pathname])

  return (
    <ShadcnSidebarMenuSubButton href={href} isActive={isActive}>
      {children}
    </ShadcnSidebarMenuSubButton>
  )
}

export default SidebarMenuSubButton
