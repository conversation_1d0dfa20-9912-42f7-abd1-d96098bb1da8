"use client"

import { <PERSON> } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useMemo } from "react"
import { SidebarMenuButton as ShadcnSidebarMenuButton } from "@/components/ui/sidebar"
import { cn } from "@/lib/utils"

interface SidebarLinkProps {
  href: string
  children: React.ReactNode
  className?: string
}

const SidebarMenuButton: FC<SidebarLinkProps> = ({ href, children }) => {
  const pathname = usePathname()
  const isActive = useMemo(() => {
    return href === "/" ? pathname === href : pathname?.startsWith(href)
  }, [href, pathname])

  return (
    <ShadcnSidebarMenuButton
      asChild
      isActive={isActive}
      className={cn(
        "font-medium text-sm transition-all duration-200 h-12",
        // The active styles will be applied via the data-active attribute
        "data-[active=true]:bg-blue-50 data-[active=true]:text-blue-600 data-[active=true]:font-semibold",
        // Default and hover styles
        "text-slate-700 hover:bg-blue-500 hover:text-white dark:text-slate-300 dark:hover:bg-slate-800"
      )}
    >
      <Link href={href}>{children}</Link>
    </ShadcnSidebarMenuButton>
  )
}

export default SidebarMenuButton
