"use client"

import { FC, useMemo } from "react"
import { ChevronRight, type LucideIcon } from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"

import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar"
import SidebarMenuSubButton from "./sidebar-menu-button/sidebar-menu-sub-button"
import { usePathname } from "next/navigation"

interface Props {
  navSettings: {
    title: string
    icon?: LucideIcon
    items?: {
      title: string
      url: string
    }[]
  }
}

export const NavCollapsible: FC<Props> = ({ navSettings }) => {
  const pathname = usePathname()

  const isActive = useMemo(() => {
    return navSettings?.items?.some(
      (subItem) => subItem.url.replace(/\/$/, "") === pathname
    )
  }, [navSettings, pathname])

  return (
    <SidebarMenu>
      <Collapsible
        key={navSettings.title}
        asChild
        defaultOpen={true}
        className="group/collapsible"
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton tooltip={navSettings.title} isActive={isActive}>
              {navSettings.icon && <navSettings.icon />}
              <span>{navSettings.title}</span>
              <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              {navSettings.items?.map((subItem) => (
                <SidebarMenuSubItem key={subItem.title}>
                  <SidebarMenuSubButton href={subItem.url}>
                    {subItem.title}
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    </SidebarMenu>
  )
}
