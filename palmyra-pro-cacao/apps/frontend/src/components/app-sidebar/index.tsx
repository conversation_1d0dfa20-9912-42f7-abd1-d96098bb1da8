"use client"

import * as React from "react"
import {
  ChartColumnBig,
  Users,
  ClipboardCheck,
  Factory,
  Package,
} from "lucide-react"

import { NavUser } from "@/components/app-sidebar/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import Image from "next/image"
import { NavMain } from "./nav-main"
import { Separator } from "@/components/ui/separator"
import { OfflineSwitch } from "./offline-switch"
import { NavNotifications } from "./nav-notifications"
import { Toaster } from "sonner"

const data = {
  navMain: [
    {
      title: "Farmers",
      url: "/farmers",
      icon: Users,
    },
    {
      title: "User Management",
      url: "/user-management",
      icon: Users,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarGroup className="items-center justify-center text-center">
          <Image
            src="/images/palmyra-pro-logo.svg"
            width={70}
            height={70}
            alt="Logo"
            priority
          />
          <SidebarGroupLabel className="text-sm font-semibold">
            PALMYRA PRO
          </SidebarGroupLabel>
        </SidebarGroup>
      </SidebarHeader>
      <SidebarContent className="flex flex-col justify-between">
        <SidebarGroup>
          <NavMain items={data.navMain} />
        </SidebarGroup>
      </SidebarContent>
      <Separator />
      <SidebarFooter>
        <NavNotifications />
        <Separator />
        <OfflineSwitch />
        <Separator />
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
      <Toaster position="top-right" expand={true} richColors closeButton />
    </Sidebar>
  )
}
