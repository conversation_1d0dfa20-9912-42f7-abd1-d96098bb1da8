"use client"

import { FC } from "react"
import { type LucideIcon } from "lucide-react"

import {
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

import SidebarMenuButton from "./sidebar-menu-button/sidebar-menu-button"

interface NavItem {
  title: string
  url: string
  icon?: LucideIcon
}

interface Props {
  items: NavItem[]
}

export const NavHelpAndSupport: FC<Props> = ({ items }) => {
  return (
    <>
      <SidebarGroupLabel>HELP & SUPPORT</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.url}>
              <SidebarMenuButton href={item.url}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </>
  )
}
