"use client"

import { MapPin } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { Badge } from "./ui/badge"

interface ProfileCardProps {
  role?: string
  firstName?: string
  lastName?: string
  location?: string
  imageUrl?: string
  isSelected: boolean
  onSelect: () => void
  id?: string
}

export default function ProfileCard({
  role,
  firstName,
  lastName,
  location,
  imageUrl,
  isSelected = false,
  onSelect,
  id,
}: ProfileCardProps) {
  return (
    <div className="p-2">
      <Card
        className={cn(
          "w-full relative cursor-pointer",
          isSelected && "ring-2 ring-primary ring-offset-2"
        )}
        onClick={onSelect}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Profile Image */}
              <Avatar className="h-12 w-12">
                <AvatarImage src={imageUrl} alt="Profile picture" />
                <AvatarFallback>
                  {firstName?.[0]}
                  {lastName?.[0]}
                </AvatarFallback>
              </Avatar>
              {/* Name*/}
              <div>
                <h1 className="text-lg font-semibold">
                  {firstName} {lastName}
                </h1>
                {/* Role */}
                <Badge>{role}</Badge>
              </div>
            </div>
            <div className="flex-col justify-items-end items-center gap-2">
              {/* ID */}
              <p className="text-sm text-muted-foreground">ID : {id}</p>
              {/* Location */}
              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="h-5 w-5 flex-shrink-0" />
                <span className="truncate">{location}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
