"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"

// Generate years from 1900 to current year
const generateYearOptions = () => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let year = currentYear; year >= 1900; year--) {
    years.push(year)
  }
  return years
}

// Generate month names
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
]

// Generate days based on month and year
const getDaysInMonth = (year: number, month: number) => {
  return new Date(year, month + 1, 0).getDate()
}

interface BirthDatePickerProps {
  value?: string
  onChange?: (date: string) => void
  placeholder?: string
  className?: string
}

export default function BirthDatePicker({
  value,
  onChange,
  placeholder = "Select Date",
  className,
}: BirthDatePickerProps) {
  const [selectedYear, setSelectedYear] = useState<number | undefined>(
    undefined
  )
  const [selectedMonth, setSelectedMonth] = useState<number | undefined>(
    undefined
  )
  const [selectedDay, setSelectedDay] = useState<number | undefined>(undefined)
  const [daysInMonth, setDaysInMonth] = useState<number[]>([])
  const [popoverOpen, setPopoverOpen] = useState(false)

  const years = generateYearOptions()

  // Initialize from value prop (YYYY-MM-DD)
  useEffect(() => {
    if (value) {
      const [year, month, day] = value.split("-").map(Number)
      if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
        setSelectedYear(year)
        setSelectedMonth(month - 1)
        setSelectedDay(day)
      }
    }
  }, [value])

  // Update days when month or year changes
  useEffect(() => {
    if (selectedYear !== undefined && selectedMonth !== undefined) {
      const numDays = getDaysInMonth(selectedYear, selectedMonth)
      const days = Array.from({ length: numDays }, (_, i) => i + 1)
      setDaysInMonth(days)

      // If the currently selected day is greater than the number of days in the new month,
      // adjust the selected day to the last day of the month
      if (selectedDay && selectedDay > numDays) {
        setSelectedDay(numDays)
      }
    }
  }, [selectedYear, selectedMonth, selectedDay])

  // Update the date when year, month, or day changes
  const updateDate = () => {
    if (
      selectedYear !== undefined &&
      selectedMonth !== undefined &&
      selectedDay !== undefined &&
      onChange
    ) {
      // Format as YYYY-MM-DD
      const formattedDate = `${selectedYear}-${String(selectedMonth + 1).padStart(2, "0")}-${String(selectedDay).padStart(2, "0")}`
      onChange(formattedDate)
    }
  }

  // Effect to call updateDate when selections change
  useEffect(() => {
    if (
      selectedYear !== undefined &&
      selectedMonth !== undefined &&
      selectedDay !== undefined
    ) {
      updateDate()
    }
  }, [selectedYear, selectedMonth, selectedDay])

  const displayDate = value ? new Date(`${value}T00:00:00`) : undefined

  return (
    <div className={className}>
      <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full pl-3 text-left font-normal",
              !value && "text-muted-foreground"
            )}
          >
            {displayDate ? (
              format(displayDate, "PPP")
            ) : (
              <span>{placeholder}</span>
            )}
            <CalendarIcon className="ml-auto h-4 w-4 text-primary" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-3" align="start">
          <div className="grid gap-4">
            <div className="grid grid-cols-3 gap-2">
              {/* Year selector */}
              <div>
                <label className="text-sm font-medium mb-1 block">Year</label>
                <Select
                  value={selectedYear?.toString() || ""}
                  onValueChange={(value) => {
                    setSelectedYear(Number.parseInt(value))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent className="max-h-80">
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Month selector */}
              <div>
                <label className="text-sm font-medium mb-1 block">Month</label>
                <Select
                  value={
                    selectedMonth !== undefined ? selectedMonth.toString() : ""
                  }
                  onValueChange={(value) => {
                    setSelectedMonth(Number.parseInt(value))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    {MONTHS.map((month, index) => (
                      <SelectItem key={month} value={index.toString()}>
                        {month}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Day selector */}
              <div>
                <label className="text-sm font-medium mb-1 block">Day</label>
                <Select
                  value={selectedDay?.toString() || ""}
                  onValueChange={(value) => {
                    setSelectedDay(Number.parseInt(value))
                    // Close popover when day is selected (all fields are now filled)
                    if (
                      selectedYear !== undefined &&
                      selectedMonth !== undefined
                    ) {
                      setPopoverOpen(false)
                    }
                  }}
                  disabled={!selectedYear || selectedMonth === undefined}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Day" />
                  </SelectTrigger>
                  <SelectContent>
                    {daysInMonth.map((day) => (
                      <SelectItem key={day} value={day.toString()}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
