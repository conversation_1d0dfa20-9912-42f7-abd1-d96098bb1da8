import { openDB, type IDBPDatabase } from "idb"

// Types
export type Farmer = {
  id: string
  firstName: string
  lastName: string
  gender: string
  phone: string
  nrc: string
  maritalStatus: string
  dob?: string
  householdSize: string
  estimatedAnnualIncome: number
  sourceOfIncome: string
  createdAt?: string
  updatedAt?: string
  is_deleted?: boolean
}

export type Location = {
  id: string
  name: string
  children?: Location[]
}

// DB constants
const DB_NAME = "palmyra-pro-db-1"
const DB_VERSION = 2

export const STORE_NAMES = {
  farmersDownloaded: "farmersDownloaded",
  farmersToCreate: "farmersToCreate",
  farmersToUpdate: "farmersToUpdate",
} as const

// Init DB
let dbPromise: Promise<IDBPDatabase>

const initDB = () => {
  if (!dbPromise) {
    dbPromise = openDB(DB_NAME, DB_VERSION, {
      upgrade(db) {
        if (!db.objectStoreNames.contains(STORE_NAMES.farmersDownloaded)) {
          db.createObjectStore(STORE_NAMES.farmersDownloaded, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.farmersToCreate)) {
          db.createObjectStore(STORE_NAMES.farmersToCreate, {
            keyPath: "id",
          })
        }

        if (!db.objectStoreNames.contains(STORE_NAMES.farmersToUpdate)) {
          db.createObjectStore(STORE_NAMES.farmersToUpdate, {
            keyPath: "id",
          })
        }
      },
    })
  }
  return dbPromise
}

// Generic Functions

// Create
export const saveToIDB = async <T extends { id?: string }>(
  storeName: string,
  data: Partial<Omit<T, "createdAt" | "updatedAt" | "is_deleted">> & {
    id?: string
  }
): Promise<T> => {
  const db = await initDB()

  const item: T = {
    ...(data as any),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    is_deleted: false,
  }

  await db.put(storeName, item)
  return item
}

// Update
export const updateInIDB = async <T extends { id: string }>(
  storeName: string,
  data: Partial<Omit<T, "updatedAt">> & { id: string }
): Promise<T> => {
  const db = await initDB()

  const existingItem = await db.get(storeName, data.id)

  if (!existingItem) {
    throw new Error(`No item with id ${data.id} found in store "${storeName}"`)
  }

  const updatedItem: T = {
    ...existingItem,
    ...data,
    updatedAt: new Date().toISOString(),
  }

  await db.put(storeName, updatedItem)
  return updatedItem
}

// Read all
export const getAllFromIDB = async <T>(storeName: string): Promise<T[]> => {
  const db = await initDB()
  return db.getAll(storeName)
}

// Delete
export const deleteFromIDB = async (storeName: string, id: string) => {
  const db = await initDB()
  await db.delete(storeName, id)
}

// Store farmers downloaded from API
export const storeFarmersFromAPI = async (
  farmers: Farmer[]
): Promise<Farmer[]> => {
  try {
    const savedFarmers = await Promise.all(
      farmers.map((farmer) =>
        saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
      )
    )
    return savedFarmers
  } catch (error) {
    console.error("Error storing downloaded farmers:", error)
    throw new Error("Failed to store downloaded farmers")
  }
}

// Store new farmers created offline
export const storeOfflineCreatedFarmer = async (
  farmer: Partial<Farmer>
): Promise<Farmer> => {
  try {
    // 1. Save in farmersToCreate
    await saveToIDB<Farmer>(STORE_NAMES.farmersToCreate, farmer)

    // 2. Save in farmersDownloaded
    return await saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
  } catch (error) {
    console.error("Error storing offline-created farmer:", error)
    throw new Error("Failed to store offline-created farmer")
  }
}

// Store farmers to updated offline
export const storeOfflineUpdatedFarmer = async (
  farmer: Partial<Farmer>
): Promise<Farmer> => {
  try {
    // 1. Save in farmersToUpdate
    await saveToIDB<Farmer>(STORE_NAMES.farmersToUpdate, farmer)

    // 2. Save in farmersDownloaded
    return await saveToIDB<Farmer>(STORE_NAMES.farmersDownloaded, farmer)
  } catch (error) {
    console.error("Error storing offline-updated farmer:", error)
    throw new Error("Failed to store offline-updated farmer")
  }
}

// Clear all data from a given store
export const clearIDBStore = async (storeName: string) => {
  const db = await initDB()
  await db.clear(storeName)
}
