import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const getInitials = (firstname?: string, lastname?: string) => {
  if (!firstname && !lastname) return "?"

  const initials = [firstname?.[0] ?? "", lastname?.[0] ?? ""]
    .join("")
    .toUpperCase()

  return initials || "?"
}

export const capitalize = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export function generateUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === "x" ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export const getRoleColor = (role: string) => {
  switch (role) {
    case "Owner":
      return "bg-red-600/20 text-red-400 border-red-600/30"
    case "Factory Staff":
      return "bg-orange-600/20 text-orange-400 border-orange-600/30"
    case "Field Supervisor":
      return "bg-pink-600/20 text-pink-400 border-pink-600/30"
    case "Manager":
      return "bg-violet-600/20 text-violet-400 border-violet-600/30"
    case "Zone Lead Farmer":
      return "bg-emerald-600/20 text-emerald-400 border-emerald-600/30"
    case "Farmer":
      return "bg-blue-600/20 text-blue-400 border-blue-600/30"
    default:
      return "bg-gray-600/20 text-gray-400 border-gray-600/30"
  }
}
