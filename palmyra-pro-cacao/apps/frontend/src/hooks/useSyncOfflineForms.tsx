"use client"

import { useState } from "react"
import { getAllFromIDB, deleteFromIDB, clearIDBStore } from "@/lib/idb"
import api from "@/lib/api"

const STORE_NAMES = ["farmersToCreate", "farmersToUpdate"] as const

export function useOfflineSyncManager() {
  const [hasPendingData, setHasPendingData] = useState(false)
  const [syncError, setSyncError] = useState<string | null>(null)
  const [isSyncing, setIsSyncing] = useState(false)
  const [hasJustSynced, setHasJustSynced] = useState(false)

  // React Query mutation to send data to the server
  const mutations = {
    farmersToCreate: api.useMutation("post", "/farmers"),
    farmersToUpdate: api.useMutation("post", "/farmers/{id}"),
  }

  // Check if any pending data in any store
  const checkPendingData = async () => {
    let pending = false
    for (const storeName of STORE_NAMES) {
      const saved = await getAllFromIDB(storeName)
      if (saved.length > 0) {
        pending = true
        break
      }
    }
    setHasPendingData(pending)
  }

  // Main sync function with proper error handling and rollback
  const syncData = async (): Promise<boolean> => {
    setIsSyncing(true)
    setSyncError(null)
    let syncedSomething = false

    // Store original data for potential rollback
    const originalData = {
      farmersToCreate: await getAllFromIDB<any>("farmersToCreate"),
      farmersToUpdate: await getAllFromIDB<any>("farmersToUpdate"),
    }

    try {
      // Sync farmers and collect ID mappings
      const farmerIdMapping: Record<string, string> = {}
      const syncedFarmerIds: string[] = []

      for (const farmer of originalData.farmersToCreate) {
        try {
          const response = await mutations.farmersToCreate.mutateAsync({
            body: farmer,
          })
          const realId = response.id

          if (realId) {
            farmerIdMapping[farmer.id] = realId
            syncedFarmerIds.push(farmer.id)
            await deleteFromIDB("farmersToCreate", farmer.id)
            syncedSomething = true
          } else {
            throw new Error("No real ID returned from server")
          }
        } catch (error: any) {
          console.error(`❌ Error syncing farmer ${farmer.id}:`, error)
          // Stop the entire sync process if farmers fail
          throw new Error(
            `Failed to sync farmer ${farmer.id}: ${error.message}`
          )
        }
      }

      // Clear downloaded stores if sync was successful
      if (syncedSomething) {
        await clearIDBStore("farmersDownloaded")
      }

      setHasJustSynced(true)
      setHasPendingData(false)

      setTimeout(() => setHasJustSynced(false), 2000)
    } catch (error: any) {
      console.error("❌ Sync failed, attempting rollback:", error)
      setSyncError(error.message || "Unknown error")
    } finally {
      setIsSyncing(false)
    }

    return syncedSomething
  }

  return {
    hasPendingData,
    syncData,
    isSyncing,
    syncError,
    hasJustSynced,
    setHasJustSynced,
    checkPendingData,
  }
}
