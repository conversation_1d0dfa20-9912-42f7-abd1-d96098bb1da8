"use client"

import { <PERSON><PERSON>, Avatar<PERSON>allback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Users,
  UserPlus,
  Edit,
  Loader,
  Search,
  AlertCircle,
  Filter,
} from "lucide-react"
import { getInitials, getRoleColor } from "@/lib/utils"
import api from "@/lib/api"
import { useAuth } from "../../app/providers/auth"
import { useState, useMemo } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useRouter } from "next/navigation"

export default function ProfilePage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")

  const {
    data: teamMembers,
    isLoading,
    error,
    refetch,
  } = api.useQuery("get", "/users")

  const { user } = useAuth()

  // Filtered team members based on search and role filter
  const filteredMembers = useMemo(() => {
    if (!teamMembers?.results) return []

    return teamMembers.results.filter((member) => {
      const matchesSearch =
        member.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.id.toString().includes(searchTerm)

      // const matchesRole = roleFilter === "all" || member.roleName === roleFilter

      return matchesSearch
      // && matchesRole
    })
  }, [teamMembers?.results, searchTerm, roleFilter])

  // Get unique roles for filter dropdown
  // const availableRoles = useMemo(() => {
  //   if (!teamMembers?.results) return []
  //   const roles = Array.from(
  //     new Set(teamMembers.results.map((member) => member.roleName))
  //   )

  //   return roles.filter(Boolean)
  // }, [teamMembers?.results])

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">User not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        <h2 className="flex items-center justify-center font-bold text-3xl">
          User Management
        </h2>

        {/* Filters and Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Members
              {teamMembers?.results && (
                <span className="text-sm font-normal text-muted-foreground">
                  ({filteredMembers.length} of {teamMembers.results.length})
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name or ID..."
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {availableRoles.map((role) => (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select> */}

              <Button
                className="w-full sm:w-auto"
                onClick={() => router.push("/user-management/create")}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </div>

            {/* Team Members List */}
            <div className="border rounded-lg">
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <Loader className="h-6 w-6 animate-spin text-primary mr-2" />
                  <span>Loading team members...</span>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <AlertCircle className="h-12 w-12 text-destructive mb-4" />
                  <p className="text-destructive font-medium">
                    Failed to load team members
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => refetch()}
                    className="mt-2"
                  >
                    Try Again
                  </Button>
                </div>
              ) : filteredMembers.length > 0 ? (
                <div className="divide-y">
                  {filteredMembers.map((member, index) => (
                    <div
                      key={member.id}
                      className={`p-4 hover:bg-muted/50 transition-colors ${
                        index % 2 === 0 ? "bg-background" : "bg-muted/20"
                      }`}
                    >
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-4 min-w-0 flex-1">
                          <Avatar className="h-12 w-12 flex-shrink-0">
                            {/* <AvatarImage src={member.image || ""} /> */}
                            <AvatarFallback className="bg-primary/10 text-primary font-medium">
                              {getInitials(
                                member.firstName || "",
                                member.lastName || ""
                              )}
                            </AvatarFallback>
                          </Avatar>

                          <div className="min-w-0 flex-1">
                            <h3 className="font-medium text-foreground truncate">
                              {member.firstName} {member.lastName}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              ID: {member.id}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 flex-shrink-0">
                          {/* <Badge
                            variant="outline"
                            className={`${getRoleColor(member.roleName)} whitespace-nowrap`}
                          >
                            {member.roleName}
                          </Badge> */}

                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-muted-foreground hover:text-foreground"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-12 text-center">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {searchTerm || roleFilter !== "all"
                      ? "No team members match your search criteria"
                      : "No team members found"}
                  </p>
                  {(searchTerm || roleFilter !== "all") && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm("")
                        setRoleFilter("all")
                      }}
                      className="mt-2"
                    >
                      Clear Filters
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
