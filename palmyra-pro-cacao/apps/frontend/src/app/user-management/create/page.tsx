"use client"
import { useState } from "react"
import { useF<PERSON>, type Submit<PERSON><PERSON><PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { type FormValues, formSchema } from "@repo/validation/add-user"
import { AddUserForm, ConfirmStep, CompleteStep } from "./steps"
import { type Step, Stepper } from "@/components/form/stepper"
import type { CompleteStepUserData } from "./steps/complete"
import api from "@/lib/api"
import { sendPasswordResetEmail } from "firebase/auth"
import { auth } from "@/lib/auth-client"

export default function AddUserPage() {
  const [step, setStep] = useState(1)
  const [userData, setUserData] = useState<CompleteStepUserData>()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const methods = useForm<FormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      role: { id: "", displayName: "", name: "" },
      phoneNumber: "",
      jobTitle: "",
      profileImage: undefined,
    },
  })

  const mutation = api.useMutation("post", "/users", {
    onSuccess: (response) => {
      const { firstName, lastName } = methods.getValues()

      setUserData({
        id: response.id || "",
        firstName,
        lastName,
      })
      methods.reset()
      setStep(step + 1)
      setIsLoading(false)
      setError(null)
    },
    onError: (error) => {
      console.error("API Error:", error)
      setError(error.message || "Failed to create user")
      setIsLoading(false)
    },
  })

  const stepComponents = [AddUserForm, ConfirmStep, CompleteStep]

  const stepSections = [
    { id: 1, label: "Add User", range: [1, 1] },
    { id: 2, label: "Review", range: [2, 2] },
    { id: 3, label: "Finish", range: [3, 3] },
  ]

  const stepperState: Step[] = stepSections.map(({ id, label, range }) => {
    const [start, end] = range
    return {
      id,
      label,
      status:
        step > end
          ? "completed"
          : step >= start && step <= end
            ? "current"
            : "upcoming",
    }
  })

  const moveToNextStep = async () => {
    // Trigger validation before moving to the next step
    const isValid = await methods.trigger([
      "firstName",
      "lastName",
      "email",
      "role",
      "phoneNumber",
      "jobTitle",
      "profileImage",
    ])

    if (!isValid) return

    setStep(step + 1)
  }

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsLoading(true)
    setError(null)

    const tempPassword = `Temp${Math.random().toString(36).slice(-8)}!`

    mutation.mutate({
      body: {
        password: tempPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        roleId: data.role.id,
        phone: data.phoneNumber,
        jobTitle: data.jobTitle,
      },
    })

    await sendPasswordResetEmail(auth, data.email)
  }

  const handleEdit = (sectionId: number) => {
    const stepId = stepSections.find((section) => section.id === sectionId)
      ?.range[0]

    if (!stepId) return

    setStep(stepId)
  }

  const renderStep = () => {
    const CurrentStepComponent = stepComponents[step - 1]

    return (
      <CurrentStepComponent
        handleNext={moveToNextStep}
        isLast={step === stepComponents.length - 1}
        formPath={userData && `/user-management/user#${userData.id}`}
        handleEdit={handleEdit}
        userData={userData}
        isLoading={isLoading}
      />
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="max-w-7xl mx-auto space-y-6">
        <Stepper
          steps={stepperState}
          progress={(step / stepComponents.length) * 100}
          backDisabled={!!userData}
        />
        <div className="min-h-screen bg-background p-6">
          <form onSubmit={methods.handleSubmit(onSubmit)}>{renderStep()}</form>
        </div>
      </div>
    </FormProvider>
  )
}
