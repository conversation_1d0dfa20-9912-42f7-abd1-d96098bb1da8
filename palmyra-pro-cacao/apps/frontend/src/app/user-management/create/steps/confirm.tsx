"use client"

import { type FC, useState } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/add-user"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import { DetailsValue } from "@/components/details-value"
import { Pencil } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { getInitials, getRoleColor } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  handleEdit: (sectionId: number) => void
  isLoading?: boolean
}

export const ConfirmStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  handleEdit,
  isLoading,
}) => {
  const { getValues } = useFormContext<FormValues>()
  const userData = getValues()
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  const [formData, setFormData] = useState<FormValues>({
    firstName: "",
    lastName: "",
    email: "",
    role: { id: "" },
    phoneNumber: "",
    jobTitle: "",
    profileImage: undefined,
  })

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData((prev) => ({ ...prev, profileImage: "" }))

      // Create preview
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const fullName = `${userData.firstName} ${userData.lastName}`.trim()

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Profile Preview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-16 w-16">
                <AvatarImage src={imagePreview || ""} alt={fullName} />
                <AvatarFallback className="bg-muted text-lg">
                  {fullName ? getInitials(fullName) : "?"}
                </AvatarFallback>
              </Avatar>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{fullName || "New User"}</h3>
              <p className="text-sm text-muted-foreground">
                {userData.email || "<EMAIL>"}
              </p>
              {userData.role && (
                <Badge
                  variant="outline"
                  className={`mt-2 ${getRoleColor(userData.role.displayName || "")}`}
                >
                  {userData.role.displayName}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <DetailsValue label="Firstname" value={userData.firstName} />
            </div>
            <div className="space-y-2">
              <DetailsValue label="Lastname" value={userData.lastName} />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <DetailsValue label="Email Adress" value={userData.email} />
            </div>

            <div className="space-y-2">
              <DetailsValue label="Phone Number" value={userData.phoneNumber} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role & Department */}
      <Card>
        <CardHeader>
          <CardTitle>Role & Department</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="space-y-2">
              <DetailsValue label="Role" value={userData.role.displayName} />
            </div>
          </div>

          <div className="space-y-2">
            <DetailsValue label="Job Title" value={userData.jobTitle} />
          </div>
        </CardContent>
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
