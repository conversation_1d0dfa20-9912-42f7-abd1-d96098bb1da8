"use client"

import { FC } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { NextButton } from "@/components/form/next-button"
import { useFormContext, useWatch } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FormValues } from "@repo/validation/add-user"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { PhoneInput } from "@/components/phone-input"
import api from "@/lib/api"
import { Loader } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Upload } from "lucide-react"
import { getInitials, getRoleColor } from "@/lib/utils"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const AddUserForm: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control } = useFormContext<FormValues>()
  const router = useRouter()

  // Watch form values for real-time preview
  const watchedValues = useWatch({ control })
  const fullName =
    `${watchedValues.firstName || ""} ${watchedValues.lastName || ""}`.trim()

  const { data: roles, isLoading: rolesLoading } = api.useQuery(
    "get",
    "/roles",
    {
      params: {
        query: {
          isFarmer: true,
        },
      },
    }
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Add New User</h1>
          <p className="text-muted-foreground">
            Create a new team member account
          </p>
        </div>
      </div>

      {/* Profile Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Preview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-16 w-16">
                <AvatarImage src="/placeholder.svg" alt={fullName} />
                <AvatarFallback className="bg-muted text-lg">
                  {fullName ? getInitials(fullName) : "?"}
                </AvatarFallback>
              </Avatar>
              <Button
                type="button"
                variant="outline"
                size="icon"
                className="absolute -bottom-2 -right-2 h-8 w-8 bg-transparent"
                onClick={() => document.getElementById("image-upload")?.click()}
              >
                <Upload className="h-4 w-4" />
              </Button>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                className="hidden"
              />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">{fullName || "New User"}</h3>
              <p className="text-sm text-muted-foreground">
                {watchedValues.email || "<EMAIL>"}
              </p>
              {watchedValues.role?.displayName && (
                <Badge
                  variant="outline"
                  className={`mt-2 ${getRoleColor(watchedValues.role.displayName || "")}`}
                >
                  {watchedValues.role.displayName}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormField
                control={control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      First Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter first name" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
            <div className="space-y-2">
              <FormField
                control={control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Last Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter last name" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Email Address <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        placeholder="Enter email address"
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-2">
              <FormField
                control={control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Phone Number <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <PhoneInput
                        id="phoneNumber"
                        value={field.value}
                        onChange={field.onChange}
                        className="border-gray-200"
                        placeholder="Enter phone number"
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role & Department */}
      <Card>
        <CardHeader>
          <CardTitle>Role & Job Title</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <FormField
                control={control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">
                      Select Role <span className="text-red-500">*</span>
                    </FormLabel>
                    {rolesLoading ? (
                      <div className="flex justify-center items-center gap-2 pt-1">
                        <Loader className="h-6 w-6 animate-spin text-primary" />
                        <span>Loading roles...</span>
                      </div>
                    ) : (
                      <Select
                        onValueChange={(value) =>
                          field.onChange(
                            roles?.results?.find((role) => role.id === value) ??
                              undefined
                          )
                        }
                        value={field.value?.id}
                      >
                        <FormControl>
                          <SelectTrigger className="border-gray-200 bg-background">
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {roles?.results?.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.displayName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
            <div className="space-y-2">
              <FormField
                control={control}
                name="jobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Job Title</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter jobTitle" />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => router.push(`/user-management`)}
          className="w-full mt-4"
        >
          Cancel
        </Button>
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
