"use client"

import React, { useEffect, useState, createContext, useContext } from "react"
import { useRouter, usePathname } from "next/navigation"
import { auth } from "@/lib/auth-client"
import { onAuthStateChanged, signOut } from "firebase/auth"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import api from "@/lib/api"

type DBUser = {
  id: string
  email?: string | null
  image?: string | null
  roleName?: string
  externalUID?: string
  firstName?: string | null
  lastName?: string | null
  phone?: string | null
  jobTitle?: string | null
  location?: string | null
}

type AuthContextType = {
  user: DBUser | null
  uid: string
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  uid: "",
  logout: async () => {},
})

export const useAuth = () => useContext(AuthContext)

const publicRoutes = ["/sign-in", "/reset-password"]

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [uid, setUid] = useState<string>("")
  const [firebaseChecked, setFirebaseChecked] = useState(false)

  const pathname = usePathname()
  const router = useRouter()

  const isPublicRoute = publicRoutes.some((route) => pathname.startsWith(route))

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        setUid(firebaseUser.uid)
      } else {
        setUid("")
      }
      setFirebaseChecked(true)
    })

    return () => unsubscribe()
  }, [])

  const {
    data: dbUser,
    isLoading,
    isError,
  } = api.useQuery(
    "get",
    "/users/{external_uid}",
    {
      params: {
        path: {
          external_uid: uid,
        },
      },
    },
    {
      enabled: firebaseChecked && !!uid,
    }
  )

  useEffect(() => {
    if (!firebaseChecked) return

    if (uid && dbUser && isPublicRoute) {
      router.push("/reports")
    }

    if (!uid && !isPublicRoute) {
      router.push("/sign-in")
      console.log(dbUser)
    }
  }, [firebaseChecked, uid, dbUser, isPublicRoute, router])

  const logout = async () => {
    await signOut(auth)
    setUid("")
    router.push("/sign-in")
  }

  useEffect(() => {
    if (firebaseChecked && uid && isError) {
      signOut(auth).then(() => {
        localStorage.setItem("loginError", "User not found in the database.")
        router.replace("/sign-in")
      })
    }
  }, [firebaseChecked, uid, isError, router])

  if (!firebaseChecked || (uid && isLoading)) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <p>Loading...</p>
      </div>
    )
  }

  if (firebaseChecked && uid && isError) {
    signOut(auth).then(() => {
      localStorage.setItem("loginError", "User not found in the database.")
      router.replace("/sign-in")
    })
  }

  return (
    <AuthContext.Provider value={{ user: dbUser ?? null, uid, logout }}>
      {dbUser && !isPublicRoute ? (
        <SidebarProvider>
          <AppSidebar />
          <main className="w-full">
            <SidebarTrigger className="fixed" />
            <div className="py-5 max-sm:px-4 px-10">{children}</div>
          </main>
        </SidebarProvider>
      ) : (
        children
      )}
    </AuthContext.Provider>
  )
}
