"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Edit,
  Save,
  X,
  Camera,
  Mail,
  Phone,
  Shield,
  User,
  ArrowLeft,
  Loader,
  AlertCircle,
} from "lucide-react"
import { getInitials, getRoleColor } from "@/lib/utils"
import { useAuth } from "@/app/providers/auth"
import { updateProfile, updateEmail } from "firebase/auth"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface UserProfile {
  firstName: string
  lastName: string
  email: string
  phoneNumber?: string
  jobTitle?: string
  department?: string
  roleName?: string
  profileImage?: string
}

export default function UserProfilePage() {
  const router = useRouter()
  const { user: authUser } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [editedProfile, setEditedProfile] = useState<Partial<UserProfile>>({})

  useEffect(() => {
    if (authUser) {
      const profile: UserProfile = {
        firstName: authUser.firstName || "",
        lastName: authUser.lastName || "",
        email: authUser.email || "",
        phoneNumber: authUser.phone || "",
        jobTitle: authUser.jobTitle || "",
        roleName: authUser.roleName || "",
        profileImage: authUser.image || "",
      }
      setUserProfile(profile)
      setEditedProfile(profile)
      setIsLoading(false)
    }
  }, [authUser])

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setEditedProfile((prev) => ({ ...prev, [field]: value }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Here you would typically upload to Firebase Storage
      // For now, we'll create a preview URL
      const reader = new FileReader()
      reader.onloadend = () => {
        setEditedProfile((prev) => ({
          ...prev,
          profileImage: reader.result as string,
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleCancel = () => {
    setEditedProfile(userProfile || {})
    setIsEditing(false)
    setError(null)
  }

  if (!authUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Not Authenticated</h2>
            <p className="text-muted-foreground mb-4">
              Please log in to view your profile.
            </p>
            <Button onClick={() => router.push("/login")}>Go to Login</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Profile Not Found</h2>
            <p className="text-muted-foreground mb-4">
              Unable to load your profile information.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const fullName = `${userProfile.firstName} ${userProfile.lastName}`.trim()
  const editedFullName =
    `${editedProfile.firstName || ""} ${editedProfile.lastName || ""}`.trim()

  return (
    <div className="min-h-screen bg-background p-4 md:p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold">My Profile</h1>
            <p className="text-muted-foreground">
              Manage your personal information and preferences
            </p>
          </div>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={isSaving}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button disabled={isSaving}>
                {isSaving ? (
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Changes
              </Button>
            </div>
          )}
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Profile Header */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              <div className="relative">
                <Avatar className="h-32 w-32">
                  <AvatarImage
                    src={
                      isEditing
                        ? editedProfile.profileImage
                        : userProfile.profileImage
                    }
                    alt={fullName}
                  />
                  <AvatarFallback className="text-2xl bg-muted">
                    {getInitials(isEditing ? editedFullName : fullName)}
                  </AvatarFallback>
                </Avatar>
                {isEditing && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="absolute -bottom-2 -right-2 h-10 w-10 rounded-full bg-transparent"
                    onClick={() =>
                      document.getElementById("profile-image-upload")?.click()
                    }
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                )}
                <input
                  id="profile-image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>

              <div className="flex-1 text-center md:text-left space-y-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          value={editedProfile.firstName || ""}
                          onChange={(e) =>
                            handleInputChange("firstName", e.target.value)
                          }
                          placeholder="Enter first name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          value={editedProfile.lastName || ""}
                          onChange={(e) =>
                            handleInputChange("lastName", e.target.value)
                          }
                          placeholder="Enter last name"
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h2 className="text-3xl font-bold">{fullName}</h2>
                    <p className="text-muted-foreground text-lg">
                      {userProfile.email}
                    </p>
                  </div>
                )}

                <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                  {userProfile.roleName && (
                    <Badge
                      variant="outline"
                      className={getRoleColor(userProfile.roleName || "")}
                    >
                      <Shield className="h-3 w-3 mr-1" />
                      {userProfile.roleName}
                    </Badge>
                  )}
                  {userProfile.department && (
                    <Badge
                      variant="outline"
                      className="bg-blue-600/20 text-blue-400 border-blue-600/30"
                    >
                      {userProfile.department}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                {isEditing ? (
                  <Input
                    id="email"
                    type="email"
                    value={editedProfile.email || ""}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    placeholder="Enter email address"
                  />
                ) : (
                  <p className="text-sm bg-muted p-3 rounded-md">
                    {userProfile.email}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="phoneNumber"
                  className="flex items-center gap-2"
                >
                  <Phone className="h-4 w-4" />
                  Phone Number
                </Label>
                {isEditing ? (
                  <Input
                    id="phoneNumber"
                    value={editedProfile.phoneNumber || ""}
                    onChange={(e) =>
                      handleInputChange("phoneNumber", e.target.value)
                    }
                    placeholder="Enter phone number"
                  />
                ) : (
                  <p className="text-sm bg-muted p-3 rounded-md">
                    {userProfile.phoneNumber || "Not provided"}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Professional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="jobTitle">Job Title</Label>
                {isEditing ? (
                  <Input
                    id="jobTitle"
                    value={editedProfile.jobTitle || ""}
                    onChange={(e) =>
                      handleInputChange("jobTitle", e.target.value)
                    }
                    placeholder="Enter job title"
                  />
                ) : (
                  <p className="text-sm bg-muted p-3 rounded-md">
                    {userProfile.jobTitle || "Not provided"}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="roleName">Role</Label>
                {isEditing ? (
                  <Input
                    id="roleName"
                    value={editedProfile.roleName || ""}
                    onChange={(e) =>
                      handleInputChange("roleName", e.target.value)
                    }
                    placeholder="Enter role Name"
                  />
                ) : (
                  <p className="text-sm bg-muted p-3 rounded-md">
                    {userProfile.roleName || "Not provided"}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
