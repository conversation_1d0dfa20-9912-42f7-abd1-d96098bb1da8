"use client"
import { useEffect, useState } from "react"
import { FarmerDetailsContainer } from "./farmer-details-container"
import { useOffline } from "@/hooks/use-offline"
import { FarmerDetailsOfflineContainer } from "./farmer-details-offline-container"

export default function FarmerManagement() {
  const [farmerId, setFarmerId] = useState<string | null>(null)
  const { isOffline } = useOffline()

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash
      if (hash) {
        setFarmerId(hash.replace("#", ""))
      }
    }
  }, [])

  if (!farmerId) {
    return <p className="text-center mt-10">Loading...</p>
  }

  return (
    <div className="md:bg-primary-foreground md:rounded-lg md:shadow md:border">
      {isOffline ? (
        <FarmerDetailsOfflineContainer farmerId={farmerId} />
      ) : (
        <FarmerDetailsContainer farmerId={farmerId} />
      )}
    </div>
  )
}
