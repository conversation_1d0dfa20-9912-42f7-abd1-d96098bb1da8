"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { useForm, type SubmitH<PERSON><PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Save, Loader } from "lucide-react"
import { type FormValues, formSchema } from "@repo/validation/add-farmer"
import api from "@/lib/api"
import { EditFarmerPage } from "./edit-farmer"
import { getAllFromIDB, saveToIDB } from "@/lib/idb"
import { useMutation } from "@tanstack/react-query"
import { useOffline } from "@/hooks/use-offline"

type Farmer = {
  id: string
  firstName: string
  lastName: string
  dob: string
  gender?: string
  phone?: string
  nrc?: string
  householdSize?: string
  maritalStatus?: string
  sourceOfIncome?: string
  estimatedAnnualIncome?: number
}

interface Props {
  farmerId: string
}

export const FarmerEditPage: React.FC<Props> = ({ farmerId }) => {
  const router = useRouter()
  const [isFormReady, setIsFormReady] = useState(false)
  const { isOffline } = useOffline()

  useEffect(() => {
    if (!farmerId) {
      router.push("/404")
    }
  }, [farmerId, router])

  if (!farmerId) return null

  const defaultValues = {
    firstName: "",
    lastName: "",
    dob: "",
    gender: "",
    phone: "",
    nrc: "",
    householdSize: "",
    maritalStatus: "",
    sourceOfIncome: "",
    estimatedAnnualIncome: 0,
  }

  // Initialize form with default values
  const methods = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: { ...defaultValues },
  })

  // Fetch farmer data
  const { data: farmer, isLoading: isApiLoading } = api.useQuery(
    "get",
    "/farmers/{id}",
    { params: { path: { id: farmerId } }, enabled: !isOffline }
  )

  // Update form values when farmer data is loaded
  useEffect(() => {
    const fetchFarmer = async () => {
      if (!farmerId) return

      if (isOffline) {
        const allFarmers = await getAllFromIDB("farmersDownloaded")
        const offlineFarmer = allFarmers.find(
          (f: any) => f.id === farmerId
        ) as Farmer

        if (offlineFarmer) {
          methods.reset({
            ...defaultValues,
            id: offlineFarmer.id,
            firstName: offlineFarmer.firstName,
            lastName: offlineFarmer.lastName,
            dob: offlineFarmer.dob,
            gender: offlineFarmer.gender || "",
            phone: offlineFarmer.phone || "",
            nrc: offlineFarmer.nrc || "",
            householdSize: offlineFarmer.householdSize || "",
            maritalStatus: offlineFarmer.maritalStatus || "",
            sourceOfIncome: offlineFarmer.sourceOfIncome || "",
            estimatedAnnualIncome: offlineFarmer.estimatedAnnualIncome || 0,
          })
          setIsFormReady(true)
        }
      }
    }

    fetchFarmer()
  }, [farmerId, isOffline, methods, router])

  useEffect(() => {
    if (!farmer) return
    // Convert all values to strings to avoid issues with Select components
    methods.reset({
      ...defaultValues,
      id: farmer.id,
      firstName: farmer.firstName,
      lastName: farmer.lastName,
      dob: farmer.dob,
      gender: farmer.gender || "",
      phone: farmer.phone || "",
      nrc: farmer.nrc || "",
      householdSize: farmer.householdSize || "",
      maritalStatus: farmer.maritalStatus || "",
      sourceOfIncome: farmer.sourceOfIncome || "",
      estimatedAnnualIncome: farmer.estimatedAnnualIncome || 0,
    })

    // Force update select fields
    if (farmer.gender) methods.setValue("gender", farmer.gender)
    if (farmer.maritalStatus)
      methods.setValue("maritalStatus", farmer.maritalStatus)

    setIsFormReady(true)
  }, [farmer, methods])

  const updateMutation = api.useMutation("post", "/farmers/{id}", {
    onSuccess: () => {
      router.push(`/farmers/farmer#${farmerId}`)
    },

    onError: (e) => {
      console.log(e.message)
    },
  })

  const updateMutationOffline = useMutation({
    mutationFn: async (payload: any) => {
      const offlinePayload = {
        ...payload,
      }

      await saveToIDB("farmersToUpdate", offlinePayload)
      await saveToIDB("farmersDownloaded", offlinePayload)

      return offlinePayload
    },
    onSuccess: () => {
      router.push(`/farmers/farmer#${farmerId}`)
    },

    onError: (e) => {
      console.log("Local save error:", e.message)
    },
  })

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    const payload = {
      firstName: data.firstName,
      lastName: data.lastName,
      gender: data.gender,
      dob: data.dob,
      phone: data.phone,
      nrc: data.nrc,
      householdSize: data.householdSize,
      maritalStatus: data.maritalStatus,
      estimatedAnnualIncome: data.estimatedAnnualIncome,
      sourceOfIncome: data.sourceOfIncome,
    }

    if (!isOffline) {
      updateMutation.mutate({
        params: {
          path: {
            id: data.id || "",
          },
        },
        body: payload,
      })
    } else {
      updateMutationOffline.mutate({
        id: data.id,
        ...payload,
      })
    }
  }

  if (isApiLoading || !isFormReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 flex items-center justify-center">
            <Loader className="h-6 w-6 animate-spin text-primary mr-2" />
            <p>Loading farmer data...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <FormProvider {...methods}>
      <div className="w-full max-w-4xl mx-auto">
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <EditFarmerPage />
          <div className="mt-4 flex gap-2 ">
            <Button
              variant="outline"
              onClick={() => router.push(`/farmers/farmer#${farmerId}`)}
              className="w-full"
            >
              Cancel
            </Button>
            <Button
              variant="default"
              type="submit"
              disabled={isApiLoading}
              className="w-full"
            >
              {isApiLoading ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </FormProvider>
  )
}
