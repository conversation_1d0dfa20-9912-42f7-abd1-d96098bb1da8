"use client"
import { useEffect, useState } from "react"
import { FarmerEditPage } from "./edit-farmer-container"

export default function EditFarmerManagement() {
  const [farmerId, setFarmerId] = useState<string | null>(null)

  useEffect(() => {
    if (typeof window !== "undefined") {
      const hash = window.location.hash
      if (hash) {
        setFarmerId(hash.replace("#", ""))
      }
    }
  }, [])

  if (!farmerId) {
    return <p className="text-center mt-10">Loading...</p>
  }

  return (
    <div>
      <FarmerEditPage farmerId={farmerId} />
    </div>
  )
}
