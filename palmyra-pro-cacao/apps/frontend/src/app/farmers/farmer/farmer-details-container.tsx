"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useSearchParams } from "next/navigation"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import FarmerProfile from "../components/farmer-profile"
import { <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Camera, Loader } from "lucide-react"
import { ProfilePhotoDialog } from "@/components/profile-photo-dialog"
import api from "@/lib/api"

interface Props {
  farmerId: string
}

export const FarmerDetailsContainer: React.FC<Props> = ({ farmerId }) => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isProfilePhotoDialogOpen, setIsProfilePhotoDialogOpen] =
    useState(false)
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string | null>(null) // null means no photo
  const [activeTab, setActiveTab] = useState("profile")

  const { data: farmerData, isLoading } = api.useQuery("get", "/farmers/{id}", {
    params: {
      path: {
        id: farmerId,
      },
    },
  })

  // Check URL parameters for active tab
  useEffect(() => {
    // Check if we have a tab parameter in the URL
    const tabParam = searchParams.get("tab")

    // If tab parameter exists and is valid, set it as the active tab
    if (tabParam === "profile") {
      setActiveTab(tabParam)
    }
  }, [searchParams])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center">
        <Loader className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading farmer data...</span>
      </div>
    )
  }

  if (!farmerData) {
    return (
      <div className="flex items-center justify-center">
        <CardContent className="pt-6 flex flex-col items-center text-center space-y-4">
          <p className="text-red-500 font-medium">
            An unknown error occurred while fetching the farmer data.
          </p>
          <Button className="w-full" onClick={() => router.push(`/farmers`)}>
            Back to Farmer List
          </Button>
        </CardContent>
      </div>
    )
  }

  //Handle photo upload completion
  const handlePhotoUpload = (photoUrl: string | null) => {
    setProfilePhotoUrl(photoUrl)
    setIsProfilePhotoDialogOpen(false)
  }

  // Get initials for avatar fallback
  const getInitials = () => {
    return `${farmerData.firstName.charAt(0)}${farmerData.lastName?.charAt(0)}`
  }

  return (
    <div>
      <CardHeader className="relative text-center space-y-4">
        {/* Farmer ID in top right */}
        <div className="flex justify-between gap-2">
          <Button variant="outline" onClick={() => router.push(`/farmers`)}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <span className="flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-500 border border-gray-200">
            ID: {farmerId}
          </span>
        </div>
        <div className="flex items-center justify-center gap-3">
          <div className="relative group">
            {/* Custom avatar implementation */}
            <div className="h-12 w-12 rounded-full border-2 border-gray-200 overflow-hidden flex items-center justify-center bg-blue-600 text-white">
              {profilePhotoUrl ? (
                <img
                  src={profilePhotoUrl || "/placeholder.svg"}
                  alt={`${farmerData.firstName} ${farmerData.lastName}`}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium">{getInitials()}</span>
              )}
            </div>
            <Button
              variant="secondary"
              size="icon"
              className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => setIsProfilePhotoDialogOpen(true)}
            >
              <Camera className="h-3 w-3" />
            </Button>
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 max-sm:text-xl">
              {farmerData.firstName + " " + farmerData.lastName}
            </h1>
            {/* TODO: Add farmer's role display name here when implemented */}
            <span className="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800 max-sm:text-xs">
              Farmer
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="max-sm:p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="flex h-10 w-full bg-transparent p-0">
            <TabsTrigger
              value="profile"
              className="h-10 flex-1 data-[state=active]:shadow-none data-[state=active]:border data-[state=active]:border-gray-200 data-[state=active]:text-blue-600 rounded-t-lg rounded-b-none"
            >
              Profile
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-0">
            <FarmerProfile farmerId={farmerId} farmerData={farmerData} />
          </TabsContent>
        </Tabs>
      </CardContent>

      <ProfilePhotoDialog
        open={isProfilePhotoDialogOpen}
        onOpenChange={setIsProfilePhotoDialogOpen}
        currentPhotoUrl=""
        userName={`${farmerData.firstName} ${farmerData.lastName}`}
        onPhotoUpload={handlePhotoUpload}
      />
    </div>
  )
}
