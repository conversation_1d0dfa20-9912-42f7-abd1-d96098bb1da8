"use client"

import { Phone, Loader, Edit } from "lucide-react"
import { Avatar } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { getRoleColor } from "@/lib/utils"

type Props = {
  isLoading: boolean
  offlineFarmers: any[]
}

export const FarmersListOffline = ({ isLoading, offlineFarmers }: Props) => {
  const router = useRouter()
  return (
    <Card className="border shadow rounded-lg">
      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading records...</span>
          </div>
        ) : offlineFarmers && offlineFarmers.length > 0 ? (
          <ul className="divide-y divide-gray-100">
            {offlineFarmers.map((farmer) => (
              <li
                key={farmer.id}
                className="p-4 hover:bg-gray-50 transition-colors cursor-pointer max-sm:p-2 odd:bg-primary-foreground"
                onClick={() => router.push(`/farmers/farmer#${farmer.id}`)}
              >
                <div className="flex justify-between w-full items-center gap-4 max-sm:gap-2">
                  <div className="flex max-sm:gap-2 gap-6">
                    <Avatar className="h-10 w-10 bg-blue-100 text-blue-600 items-center justify-center">
                      <span className="font-medium text-sm">
                        {farmer.firstName.charAt(0) +
                          farmer.lastName?.charAt(0)}
                      </span>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-gray-900 max-sm:text-sm">
                        {farmer.firstName + " " + farmer.lastName}
                      </h3>
                      <div className="max-sm:hidden flex items-center text-gray-500 text-sm gap-2 max-sm:gap-1 max-sm:text-xs">
                        <Phone className="h-3.5 w-3.5" />
                        <span>{farmer.phone}</span>
                      </div>
                    </div>
                  </div>

                  <Badge
                    variant="outline"
                    className={`mt-2 ${getRoleColor(farmer.roleDisplayName || "")}`}
                  >
                    {farmer.roleDisplayName}
                  </Badge>

                  <div className="flex min-md:gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-blue-600"
                      onClick={(e) => {
                        e.stopPropagation()
                        router.push(`/farmers/farmer/edit#${farmer.id}`)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="p-8 text-center text-gray-500">
            No farmers found. Try a different search term.
          </div>
        )}
      </CardContent>
    </Card>
  )
}
