"use client"

import React from "react"
import { Pencil } from "lucide-react"
import { CardContent } from "@/components/ui/card"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { DetailsValue } from "@/components/details-value"
import { FormValues } from "@repo/validation/add-farmer"
import { useOffline } from "@/hooks/use-offline"

interface FarmerDetailsProps {
  farmerData: FormValues
  handleEdit?: (section: number) => void
}

const FarmerDetails: React.FC<FarmerDetailsProps> = ({
  farmerData,
  handleEdit,
}) => {
  const { isOffline } = useOffline()
  return (
    <CardContent className="max-sm:p-2 space-y-6">
      <Accordion
        type="multiple"
        defaultValue={[
          "Personal Information",
          "Economic Information",
          "Professional Information & Location",
        ]}
        className="w-full"
      >
        {/* Personal Information Section */}
        <AccordionItem value="Personal Information">
          <AccordionTrigger className="group py-2">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-red-500" />
              <span className="font-semibold">Personal Information</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(1)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
              <DetailsValue label="First Name" value={farmerData.firstName} />
              <DetailsValue label="Last Name" value={farmerData.lastName} />
              <DetailsValue label="Gender" value={farmerData.gender} />
              <DetailsValue label="Birth Date" value={farmerData.dob} />
              <DetailsValue label="Phone Number" value={farmerData.phone} />
              <DetailsValue label="NRC Number" value={farmerData.nrc} />
              <DetailsValue
                label="Household Size"
                value={farmerData.householdSize || "Not provided"}
              />
              <DetailsValue
                label="Marital Status"
                value={farmerData.maritalStatus || "Not provided"}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
        {/* Economic Information Section */}
        <AccordionItem value="Economic Information">
          <AccordionTrigger className="group py-2">
            <div className="flex items-center gap-2">
              <span className="h-2 w-2 rounded-full bg-green-500" />
              <span className="font-semibold">Economic Information</span>
            </div>
            {handleEdit && (
              <div
                role="button"
                className="ml-auto h-7 w-7 rounded-md hover:bg-accent hover:text-accent-foreground p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEdit(1)
                }}
              >
                <Pencil className="h-4 w-4" />
              </div>
            )}
          </AccordionTrigger>
          <AccordionContent className="space-y-4 pt-4">
            <div className="bg-background border shadow rounded-lg p-6 grid grid-cols-2 gap-4">
              <DetailsValue
                label="Estimated Annual Income"
                value={farmerData.estimatedAnnualIncome || "Not provided"}
              />
              <DetailsValue
                label="Source of Income"
                value={farmerData.sourceOfIncome || "Not provided"}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </CardContent>
  )
}

export default FarmerDetails
