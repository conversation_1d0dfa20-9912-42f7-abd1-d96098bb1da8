"use client"

import { FC } from "react"
import { useRout<PERSON> } from "next/navigation"
import { NextButton } from "@/components/form/next-button"
import { useFormContext } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FormValues } from "@repo/validation/add-farmer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { PhoneInput } from "@/components/phone-input"
import { NrcInput } from "@/components/nrc-input"
import BirthDatePicker from "@/components/birth-date-picker"
import { countryDialCodes } from "@repo/consts"
import api from "@/lib/api"
import { useOffline } from "@/hooks/use-offline"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  isLoading?: boolean
}

export const AddFarmerForm: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  isLoading,
}) => {
  const { control } = useFormContext<FormValues>()
  const router = useRouter()

  return (
    <div>
      <Card className="w-full max-w-4xl mx-auto bg-primary-foreground">
        <CardHeader className="flex flex-row justify-between">
          <CardTitle className="text-xl font-bold">Add a Farmer</CardTitle>
        </CardHeader>

        <CardContent className="mb-6">
          <h3 className="text-base font-semibold mb-4">Personal Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <FormField
              control={control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    First Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="ex: John"
                      className="border-gray-200 bg-background"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Last Name <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="ex: Doe"
                      className="border-gray-200 bg-background"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="gender"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Gender <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="border-gray-200 bg-background">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="male">Male</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="dob"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Birth Date <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <BirthDatePicker
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Phone Number <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value}
                      onChange={field.onChange}
                      className="border-gray-200"
                      placeholder={`+${countryDialCodes.Zambia} 123 456 789`}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="nrc"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    NRC Number <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <NrcInput
                      value={field.value}
                      onChange={field.onChange}
                      className="border-gray-200"
                      placeholder="123456/78/9"
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="householdSize"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Household Size <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="ex: 5"
                      className="border-gray-200 bg-background"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="maritalStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Marital Status <span className="text-red-500">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger className="border-gray-200 bg-background">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="single">Single</SelectItem>
                      <SelectItem value="married">Married</SelectItem>
                      <SelectItem value="divorced">Divorced</SelectItem>
                      <SelectItem value="widowed">Widowed</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>
        </CardContent>

        <CardContent className="mb-6">
          <h3 className="text-base font-semibold mb-4">Economic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={control}
              name="estimatedAnnualIncome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Estimated Annual Income{" "}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="ex: 1200 ZD"
                      className="border-gray-200 bg-background"
                      {...field}
                      value={field.value ?? ""}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        field.onChange(value)
                      }}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="sourceOfIncome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Source of Income <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="ex: Farming, Trading"
                      className="border-gray-200 bg-background"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => router.push(`/farmers`)}
          className="w-full mt-4"
        >
          Cancel
        </Button>
        <NextButton
          isLast={isLast}
          handleNext={handleNext}
          formPath={formPath}
          isLoading={isLoading}
        />
      </div>
    </div>
  )
}
