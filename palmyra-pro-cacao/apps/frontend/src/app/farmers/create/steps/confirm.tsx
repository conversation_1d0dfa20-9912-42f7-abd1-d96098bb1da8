"use client"

import type { FC } from "react"
import { useFormContext } from "react-hook-form"
import type { FormValues } from "@repo/validation/add-farmer"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { NextButton } from "@/components/form/next-button"
import FarmerDetails from "../../components/farmer-details"

interface Props {
  handleNext: () => void
  isLast: boolean
  formPath?: string
  handleEdit: (sectionId: number) => void
  isLoading?: boolean
}

export const ConfirmStep: FC<Props> = ({
  handleNext,
  isLast,
  formPath,
  handleEdit,
  isLoading,
}) => {
  const { getValues } = useFormContext<FormValues>()
  const farmerData = getValues()

  return (
    <div className="space-y-4">
      <Card className="bg-primary-foreground border">
        <CardHeader className="flex flex-row justify-between items-center">
          <CardTitle>Farmer Summary</CardTitle>
        </CardHeader>
        <FarmerDetails farmerData={farmerData} handleEdit={handleEdit} />
      </Card>

      <NextButton
        isLast={isLast}
        handleNext={handleNext}
        formPath={formPath}
        isLoading={isLoading}
      />
    </div>
  )
}
