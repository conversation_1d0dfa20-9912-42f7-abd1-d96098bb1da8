"use client"

import { useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { Loader2, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  type ResetPasswordFormValues,
  resetPasswordSchema,
} from "@repo/validation/reset-password"
import { ResetPasswordForm } from "./components/reset-password-form"
import { Button } from "@/components/ui/button"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, FormProvider } from "react-hook-form"

type PageState =
  | { status: "form"; token: string | null }
  | { status: "error"; message: string }
  | { status: "updating" }
  | { status: "success" }
  | { status: "failed"; message: string }

export default function ResetPasswordPage() {
  const token = new URLSearchParams(window.location.search).get("token")
  const [pageState, setPageState] = useState<PageState>({
    status: "form",
    token,
  })
  const router = useRouter()

  if (!token) {
    setPageState({
      status: "error",
      message: "No reset token found.",
    })
    return
  }

  const methods = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
  })

  const onSubmit = async (data: ResetPasswordFormValues) => {
    // TODO: Implement password reset logic here
    // if (token === null) {
    //   setPageState({
    //     status: "error",
    //     message: "Invalid URL.",
    //   })
    //   return
    // }
    // setPageState({ status: "updating" })
    // try {
    //   const result = await authClient.resetPassword({
    //     newPassword: data.password,
    //     token: token,
    //   })
    //   if (result.data?.status) {
    //     setPageState({
    //       status: "success",
    //     })
    //     return
    //   } else {
    //     setPageState({
    //       status: "failed",
    //       message: "Password reset failed.",
    //     })
    //     return
    //   }
    // } catch (error) {
    //   console.error("Password update failed:", error)
    //   setPageState({
    //     status: "failed",
    //     message:
    //       error instanceof Error
    //         ? error.message
    //         : "Password reset failed. Please try again.",
    //   })
    // }
  }

  const renderContent = () => {
    switch (pageState.status) {
      case "form":
        return <ResetPasswordForm onSubmit={onSubmit} />

      case "updating":
        return (
          <div className="flex flex-col items-center gap-4 py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <div className="text-center">
              <h2 className="text-lg font-semibold">Updating Password</h2>
              <p className="text-sm text-muted-foreground">
                Please wait while we update your password...
              </p>
            </div>
          </div>
        )

      case "success":
        return (
          <div className="flex flex-col items-center gap-6 py-2">
            <CheckCircle className="h-16 w-16 text-green-500" />
            <div className="text-center space-y-2">
              <h2 className="text-xl font-semibold text-green-700">
                Password Reset Successful!
              </h2>
              <p className="text-sm text-muted-foreground">
                Your password has been updated successfully. You can now sign in
                with your new password.
              </p>
            </div>
            <Button
              onClick={() => router.push("/sign-in")}
              className="w-full bg-green-600 hover:bg-green-700"
              size="lg"
            >
              Go to Sign In
            </Button>
          </div>
        )

      case "error":
        return (
          <div className="flex flex-col items-center gap-6 py-8">
            <XCircle className="h-12 w-12 text-red-500" />
            <div className="text-center">
              <h2 className="text-lg font-semibold text-red-700">
                Verification Failed
              </h2>
              <p className="text-sm text-red-600 mb-4">{pageState.message}</p>
            </div>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Reset tokens are only valid for 24 hours. If you haven't
                completed the process within that time, please contact the
                administrator to request a new password reset link.
              </AlertDescription>
            </Alert>
            <div className="flex flex-col gap-3 w-full">
              <Button
                onClick={() => router.push("/sign-in")}
                className="w-full"
              >
                Back to Sign In
              </Button>
            </div>
          </div>
        )

      case "failed":
        return (
          <div className="space-y-6">
            <div className="flex flex-col items-center gap-4">
              <XCircle className="h-12 w-12 text-red-500" />
              <div className="text-center">
                <h2 className="text-lg font-semibold text-red-700">
                  Password Reset Failed
                </h2>
                <p className="text-sm text-red-600">{pageState.message}</p>
              </div>
            </div>

            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please check your password requirements and try again. If the
                problem persists, contact support.
              </AlertDescription>
            </Alert>

            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-4">
                Try again with a different password :
              </p>
              <ResetPasswordForm onSubmit={onSubmit} />
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <FormProvider {...methods}>
      <div className="flex min-h-screen flex-col items-center justify-center bg-muted p-6">
        <div className="w-full max-w-md">
          <Card className="shadow-lg">
            <CardHeader className="text-center pb-6">
              <div className="flex justify-center mb-4">
                <Image
                  src="/images/palmyra-pro-logo.svg"
                  width={80}
                  height={80}
                  alt="Palmyra Pro Logo"
                  priority
                  className="rounded-lg"
                />
              </div>
              <CardTitle className="text-2xl font-bold">PALMYRA PRO</CardTitle>
              <CardDescription>Reset your account password</CardDescription>
            </CardHeader>
            <CardContent>{renderContent()}</CardContent>
          </Card>
        </div>
      </div>
    </FormProvider>
  )
}
