import admin from "firebase-admin"
import { getAuth } from "firebase-admin/auth"

const tenantId = process.argv[2]
const uid = process.argv[3]

if (!tenantId || !uid) {
  console.error("Usage: node deleteAdmin.js <TenantID> <UID>")
  process.exit(1)
}

admin.initializeApp({
  credential: admin.credential.cert("./serviceAccount.json"),
})

const tenantAuth = getAuth().tenantManager().authForTenant(tenantId)

tenantAuth
  .deleteUser(uid)
  .then(() => {
    console.log(
      `✅ User with UID ${uid} deleted successfully from tenant ${tenantId}`
    )
  })
  .catch((error) => {
    console.error("❌ Error deleting user:", error)
  })
