# 👩‍💻 Admin User Management with Firebase Admin SDK

This guide explains how to create and manage admin users using the Firebase Admin SDK in a multi-tenant setup.

---

## 🛠️ Usage

### ➕ Create an Admin User

To create an admin user for a specific tenant, run the following command:

```bash
node apps/cli/createTenantAdmin.js <TenantID> <email> <password>
```

### ➕ Get user admin UID

To have the UID of a admin user, run the following command:

```bash
node apps/cli/getUserUid.js <TenantID> <email>
```

### ➕ Get users admin list

To have the list of a specific tenant, run the following command:

```bash
node apps/cli/listTenantUsers.js <TenantID>
```

### ❌ Delete Admin User

To delete an admin user from Firebase Auth within a specific tenant:

```bash
node apps/cli/deleteAdmin.js <TenantID> <UID>
```
