import admin from "firebase-admin"
import fs from "fs"

// Load credentials
const serviceAccount = JSON.parse(
  fs.readFileSync("./serviceAccount.json", "utf8")
)

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
})

const auth = admin.auth()

/**
 * Retrieves a user's UID using their email and tenant ID
 */
async function getUid(tenantId, email) {
  try {
    const tenantAuth = auth.tenantManager().authForTenant(tenantId)
    const user = await tenantAuth.getUserByEmail(email)

    console.log(`✅ UID of ${email} in tenant ${tenantId}:`)
    console.log(user.uid)
  } catch (err) {
    console.error("❌ User not found:", err.message)
    process.exit(1)
  }
}

const [tenantId, email] = process.argv.slice(2)

if (!tenantId || !email) {
  console.error("❗ Usage: node getUserUid.js <tenantId> <email>")
  process.exit(1)
}

getUid(tenantId, email)
