services:
  #  frontend:
  #    container_name: cacao-frontend
  #    build:
  #      context: .
  #      dockerfile: ./apps/frontend/Dockerfile
  #      args:
  #        NEXT_PUBLIC_BACKEND_URL: ${NEXT_PUBLIC_BACKEND_URL}
  #    env_file:
  #      - ./.env.dockercompose
  #    restart: on-failure
  #    ports:
  #      - "9080:3000"
  #    networks:
  #      - cacao_network

  api:
    container_name: cacao-api
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    env_file:
      - ./.env.dockercompose
    restart: on-failure
    ports:
      - "4001:3000"
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./serviceAccount.json:/serviceAccount.json:ro
    environment:
      - FIREBASE_ADMIN_KEY_PATH=/serviceAccount.json
    networks:
      - cacao_network

  db:
    image: postgres:17.4
    restart: on-failure
    #    command: ["postgres", "-c", "log_statement=all"]
    environment:
      POSTGRES_PASSWORD: mysecretpassword
      POSTGRES_DB: palmira_pro_db
    healthcheck:
      test:
        ["CMD", "pg_isready", "-q", "-U", "postgres", "-d", "palmira_pro_db"]
      interval: 2s
      timeout: 5s
      retries: 10
    volumes:
      - cacao_pgdata:/var/lib/postgresql/data
      - ./packages/db/schema:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - cacao_network

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  cacao_network:
#    external: true

volumes:
  cacao_pgdata:
