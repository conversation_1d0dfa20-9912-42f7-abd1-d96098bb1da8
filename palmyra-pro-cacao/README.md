# Pa<PERSON>ra Pro Cacao

- [Palmyra Pro SaaS Documentation](./docs/saas/index.md)

## Techs MVP1

- Frontend
    - npm
    - Typescript 5
    - NodeJS 22
    - Tuborepo
    - Nextjs 14
    - React 18
    - <PERSON><PERSON><PERSON> 3
    - Shadcn
- Backend
    - OpenAPI 3.0
    - Go Lang 1.24

## DEV environment

All the en parts are available from Docker Compose. To work in the frontend, all services are necessary except the
frontend.

So these are the steps, running from the root :

```shell
# Start Auth, API and all the dependencies.
# Use `-d` to run in the background and `docker compose logs -f` to tail logs later  
docker compose up auth api

# Start only the frontend en dev mode. 
turbo run dev --filter frontend
```

To clean up:

```bash
docker rm server-api auth palmyra-pro-db-1 palmyra-pro-adminer-1
docker volume rm palmyra-pro_pgdata
turbo clean
find . -name "node_modules" -type d -prune -exec rm -rf {} +
```

Generate code from cli:

```bash
npm install
turbo run generate
```

## Create environments

```shell
gcloud auth list
gcloud auth login

gcloud config set project natures-nectar-dev

gh workflow run .github/workflows/create-or-update-environment.yml \
 -r angelcervera/ci-cd-2 \
 -f environment="development" \
 -f shortName="test-build" \
 -f dbInstance="naturesnectar-db-dev"
```
