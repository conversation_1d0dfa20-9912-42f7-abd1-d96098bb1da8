{"name": "@repo/api-specs", "version": "0.0.0", "private": true, "type": "module", "main": "./src/schema.d.ts", "types": "./src/schema.d.ts", "exports": {".": {"types": "./src/schema.d.ts"}, "./schemas/*": "./schemas/*"}, "scripts": {"lint": "eslint .", "generate-types": "npx openapi-typescript schemas/openapi.yaml -o src/schema.d.ts", "generate": "npm run generate-types", "prebuild": "npm run generate", "dev": "echo 'Add dev script here'", "test": "echo 'Add test script here'"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "typescript": "^5.3.3"}, "dependencies": {"openapi-typescript": "^7.6.1"}}