/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/farmers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get farmers
         * @description Get farmers with filters
         */
        get: operations["getFarmers"];
        put?: never;
        /**
         * Create a new farmer
         * @description Creates a farmer
         */
        post: operations["createFarmer"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/offline": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Download all farmers for offline usage. */
        get: operations["downloadFarmersOffline"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Get Farmer by ID
         * @description Returns a farmer by ID
         */
        get: operations["getFarmerById"];
        put?: never;
        /**
         * Update a existing farmer
         * @description Update a existing farmer
         */
        post: operations["updateFarmer"];
        /**
         * Soft delete Farmer by ID
         * @description Execute a soft delete of the farmer.
         */
        delete: operations["deleteFarmerById"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/farmers/search": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * Search farmers
         * @description Search farmers with filters
         */
        get: operations["searchFarmers"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Search Users */
        get: operations["getUsers"];
        put?: never;
        /**
         * Create a new user
         * @description Creates a user
         */
        post: operations["CreateUser"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/{external_uid}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Get user by Firebase UID */
        get: operations["getByExternalUID"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        Error: components["schemas"]["errors"];
        Farmer: components["schemas"]["details"];
        FarmerList: {
            results?: components["schemas"]["details"][];
            /** @description Total number of farmers */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        User: {
            id: string;
            externalUID?: string;
            firstName?: string;
            lastName?: string;
            email?: string;
        };
        UserList: {
            results?: components["schemas"]["User"][];
            /** @description Total number of items */
            total?: number;
            /** @description Current page number */
            page?: number;
            /** @description Number of items per page */
            pageSize?: number;
            /** @description Total number of pages */
            totalPages?: number;
        };
        /** @example {
         *       "code": "INVALID_REQUEST",
         *       "message": "The request contains invalid parameters"
         *     } */
        errors: {
            /** @description Error code identifier */
            code: string;
            /** @description Human-readable error message */
            message: string;
        };
        details: {
            /** @description Unique identifier for the farmer */
            id: string;
            /** @description First name of the farmer */
            firstName: string;
            /** @description Last name of the farmer */
            lastName: string;
            /** @description Gender of the farmer */
            gender: string;
            /** @description Phone number of the farmer */
            phone: string;
            /** @description Marital status of the farmer */
            maritalStatus: string;
            /**
             * Format: date
             * @description Date of birth of the farmer
             */
            dob?: string;
            /** @description Size of the farmer's household */
            householdSize: string;
            /**
             * Format: float
             * @description Estimated annual income of the farmer
             */
            estimatedAnnualIncome: number;
            /** @description Source of income for the farmer */
            sourceOfIncome: string;
            /** @description National ID number */
            nrc: string;
            /**
             * Format: date-time
             * @description When the farmer was created
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description When the farmer was last updated
             */
            updatedAt: string;
        };
        creation_update: {
            /** @description First name of the farmer */
            firstName: string;
            /** @description Last name of the farmer */
            lastName: string;
            /** @description Gender of the farmer */
            gender: string;
            /** @description Phone number of the farmer */
            phone: string;
            /** @description Marital status of the farmer */
            maritalStatus: string;
            /**
             * Format: date
             * @description Date of birth of the farmer
             */
            dob?: string;
            /** @description Size of the farmer's household */
            householdSize: string;
            /**
             * Format: float
             * @description Estimated annual income of the farmer
             */
            estimatedAnnualIncome: number;
            /** @description Source of income for the farmer */
            sourceOfIncome: string;
            /** @description National ID number */
            nrc: string;
        };
        "creation_update-2": {
            password: string;
            /** @description First name of the user */
            firstName: string;
            /** @description Last name of the user */
            lastName: string;
            /** @description Email of the user */
            email: string;
            /** @description Phone number of the user */
            phone: string;
            /** @description Job Title of the user */
            jobTitle: string;
            /** @description ID of the role associated with the user */
            roleId: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    getFarmers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter by business ID */
                businessId?: string;
                /** @description Filter by creator ID */
                createdBy?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    createFarmer: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update"];
            };
        };
        responses: {
            /** @description Farmer created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new farmer */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    downloadFarmersOffline: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getFarmerById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Farmer found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["details"];
                };
            };
            /** @description Farmer not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    updateFarmer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update"];
            };
        };
        responses: {
            /** @description Farmer created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new farmer */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    deleteFarmerById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Farmer ID */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Farmer deleted */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Farmer not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    searchFarmers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Filter by business ID */
                businessId?: string;
                /** @description Filter by creator ID */
                createdBy?: string;
                /** @description Filter created after this date, inclusive */
                createdAtFrom?: string;
                /** @description Filter created before this date, exclusive */
                createdAtTo?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Search results */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["FarmerList"];
                };
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getUsers: {
        parameters: {
            query?: {
                /** @description Fuzzy search query text */
                query?: string;
                /** @description Page number */
                page?: number;
                /** @description Number of items per page */
                pageSize?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description A list of users */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserList"];
                };
            };
            /** @description Invalid request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unexpected error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    CreateUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["creation_update-2"];
            };
        };
        responses: {
            /** @description User created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @description Unique identifier for the new user */
                        id?: string;
                    };
                };
            };
            /** @description Error */
            default: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["errors"];
                };
            };
        };
    };
    getByExternalUID: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                external_uid: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description User found */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["User"];
                };
            };
            /** @description User not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        code?: string;
                        message?: string;
                    };
                };
            };
        };
    };
}
