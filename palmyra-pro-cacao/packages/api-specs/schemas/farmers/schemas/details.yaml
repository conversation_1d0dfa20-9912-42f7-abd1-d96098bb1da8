type: object
required:
  - id
  - firstName
  - lastName
  - gender
  - phone
  - maritalStatus
  - householdSize
  - estimatedAnnualIncome
  - sourceOfIncome
  - nrc
  - createdAt
  - updatedAt
properties:
  id:
    type: string
    description: Unique identifier for the farmer
  firstName:
    type: string
    description: First name of the farmer
  lastName:
    type: string
    description: Last name of the farmer
  gender:
    type: string
    description: Gender of the farmer
  phone:
    type: string
    description: Phone number of the farmer
  maritalStatus:
    type: string
    description: Marital status of the farmer
  dob:
    type: string
    format: date
    description: Date of birth of the farmer
  householdSize:
    type: string
    description: Size of the farmer's household
  estimatedAnnualIncome:
    type: number
    format: float
    description: Estimated annual income of the farmer
  sourceOfIncome:
    type: string
    description: Source of income for the farmer
  nrc:
    type: string
    description: National ID number
  createdAt:
    type: string
    format: date-time
    description: When the farmer was created
  updatedAt:
    type: string
    format: date-time
    description: When the farmer was last updated
