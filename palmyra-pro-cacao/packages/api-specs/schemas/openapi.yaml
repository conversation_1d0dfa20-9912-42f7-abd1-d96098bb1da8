openapi: 3.0.4

info:
  title: Palmyra Pro Cocoa API
  description: Palmyra Pro Cocoa API
  version: 0.0.1

servers:
  - url: /
  # TODO: Add when staging is ready
  # - url: https://staging-nn-api-463523546.asia-southeast1.run.app/
  #   description: Staging server
  - url: http://localhost:4000
    description: Local environment

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # FIXME: We should split the definition in multiple files, but looks like swagger-ui-express does not like that.
    Error:
      $ref: "./commons/schema/errors.yaml"

    Farmer:
      $ref: "./farmers/schemas/details.yaml"

    FarmerList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/Farmer"
        total:
          type: integer
          description: Total number of farmers
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

    User:
      type: object
      required: ["id"]
      properties:
        id:
          type: string
        externalUID:
          type: string
        # FIXME: This should be a RoleDetails object
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string

    UserList:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: "#/components/schemas/User"
        total:
          type: integer
          description: Total number of items
        page:
          type: integer
          description: Current page number
        pageSize:
          type: integer
          description: Number of items per page
        totalPages:
          type: integer
          description: Total number of pages

security:
  - BearerAuth: []

paths:
  /farmers:
    post:
      summary: Create a new farmer
      description: Creates a farmer
      operationId: createFarmer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./farmers/schemas/creation_update.yaml"

      responses:
        "201":
          description: Farmer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new farmer
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get farmers
      description: Get farmers with filters
      operationId: getFarmers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: businessId
          in: query
          description: Filter by business ID
          required: false
          schema:
            type: string
        - name: createdBy
          in: query
          description: Filter by creator ID
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10

      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/offline:
    get:
      summary: Download all farmers for offline usage.
      operationId: downloadFarmersOffline
      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"

        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/{id}:
    delete:
      summary: Soft delete Farmer by ID
      description: Execute a soft delete of the farmer.
      operationId: deleteFarmerById
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Farmer deleted
        404:
          description: Farmer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: Update a existing farmer
      description: Update a existing farmer
      operationId: updateFarmer
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./farmers/schemas/creation_update.yaml"

      responses:
        "200":
          description: Farmer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new farmer
        404:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Get Farmer by ID
      description: Returns a farmer by ID
      operationId: getFarmerById
      parameters:
        - name: id
          in: path
          description: Farmer ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Farmer found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Farmer"
        404:
          description: Farmer not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /farmers/search:
    get:
      summary: Search farmers
      description: Search farmers with filters
      operationId: searchFarmers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: businessId
          in: query
          description: Filter by business ID
          required: false
          schema:
            type: string
        - name: createdBy
          in: query
          description: Filter by creator ID
          required: false
          schema:
            type: string
        - name: createdAtFrom
          in: query
          description: Filter created after this date, inclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: createdAtTo
          in: query
          description: Filter created before this date, exclusive
          required: false
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10

      responses:
        "200":
          description: Search results
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FarmerList"
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /users:
    post:
      summary: Create a new user
      description: Creates a user
      operationId: CreateUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "./auth/schemas/creation_update.yaml"
      responses:
        "201":
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: Unique identifier for the new user
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    get:
      summary: Search Users
      operationId: getUsers
      parameters:
        - name: query
          in: query
          description: Fuzzy search query text
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          required: false
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
      responses:
        "200":
          description: A list of users
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserList"
        "400":
          description: Invalid request
        default:
          description: Unexpected error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /users/{external_uid}:
    get:
      summary: Get user by Firebase UID
      operationId: getByExternalUID
      parameters:
        - name: external_uid
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: User found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "404":
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                  message:
                    type: string
