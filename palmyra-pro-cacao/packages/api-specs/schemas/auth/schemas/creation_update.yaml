type: object
required: [password, firstName, lastName, email, phone, jobTitle, roleId]
properties:
  password:
    type: string
  firstName:
    type: string
    description: First name of the user
  lastName:
    type: string
    description: Last name of the user
  email:
    type: string
    description: Email of the user
  phone:
    type: string
    description: Phone number of the user
  jobTitle:
    type: string
    description: Job Title of the user
  roleId:
    type: string
    description: ID of the role associated with the user
