-- DROP DATABASE IF EXISTS palmira_pro_db;

-- CREATE DATABASE palmira_pro_db;

-- \c palmira_pro_db
-- public."user" definition

-- Drop table

-- DROP TABLE "user";

CREATE TABLE "user" (
    external_uid varchar                              UNIQUE,
    id           varchar                              NOT NULL,
    first_name   varchar(200)                         NULL,
    last_name    varchar(200)                         NULL,
    email        varchar(200)                         NOT NULL,
    updated_at   timestamp DEFAULT CURRENT_TIMESTAMP   NULL,
    created_at   timestamp DEFAULT CURRENT_TIMESTAMP   NULL,
    CONSTRAINT user_email_key UNIQUE (email),
    CONSTRAINT user_pkey PRIMARY KEY (id)
);

-- public.farmer definition

-- Drop table

-- DROP TABLE farmer;

CREATE TABLE farmer (
    id                       text                                NOT NULL,
    first_name               text                                NULL,
    last_name                text                                NULL,
    gender                   text                                NULL,
    phone                    text                                NULL,
    nrc                      text                                NULL,
    marital_status           text                                NULL,
    dob                      text                                NULL,
    household_size           text                                NULL,
    estimated_annual_income  float4                              NULL,
    source_of_income         text                                NULL,
    created_at               timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    updated_at               timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    is_deleted               bool DEFAULT false,
    CONSTRAINT farmer_pkey PRIMARY KEY (id)
);
