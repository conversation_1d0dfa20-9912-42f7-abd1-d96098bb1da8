{"name": "@repo/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {".": "./index.js", "./next": "./next.js", "./react": "./react.js", "./remix": "./remix.js", "./vite": "./vite.js"}, "devDependencies": {"@eslint/js": "^9.20.0", "@next/eslint-plugin-next": "^15.1.6", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-turbo": "^2.3.4", "globals": "^15.15.0", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0"}}