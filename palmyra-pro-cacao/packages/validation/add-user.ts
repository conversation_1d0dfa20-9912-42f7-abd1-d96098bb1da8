import { z } from "zod"

export const formSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z.string().min(1, { message: "Email is required" }),
  role: z.object({
    id: z.string().min(1),
    name: z.string().optional(),
    displayName: z.string().optional(),
  }),
  phoneNumber: z
    .string()
    .min(1, { message: "Phone number is required" })
    .regex(/^\+260 [0-9]{3} [0-9]{3} [0-9]{3}$/, {
      message: `Phone number must be in format: +260 999 999 999`,
    }),
  jobTitle: z.string(),
  profileImage: z.string().optional(),
})

export type FormValues = z.infer<typeof formSchema>
