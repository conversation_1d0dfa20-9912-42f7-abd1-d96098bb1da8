import { z } from "zod"

export const formSchema = z.object({
  id: z.string().optional(),
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  gender: z.string().min(1, { message: "Gender is required" }),
  dob: z
    .string({ required_error: "Birth date is required" })
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  phone: z
    .string()
    .min(1, { message: "Phone number is required" })
    .max(15, { message: "Phone number must be at most 15 characters" }),
  nrc: z.string().min(1, { message: "NRC number is required" }),
  householdSize: z.string().min(1, { message: "Household size is required" }),
  maritalStatus: z.string().min(1, { message: "Marital status is required" }),
  estimatedAnnualIncome: z
    .number({
      required_error: "Estimated Annual Income is required",
    })
    .min(0, { message: "Estimated annual income must be positive" }),
  sourceOfIncome: z
    .string()
    .min(1, { message: "Source of income is required" }),
})

export type FormValues = z.infer<typeof formSchema>
