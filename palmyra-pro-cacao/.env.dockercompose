# GLOBALS
ENV_SHORT_NAME: local

# DB
DB_NAME: "palmira_pro_db"
DB_HOST: "db"
DB_PORT: 5432
DB_USER: "postgres"
DB_PASSWORD: "mysecretpassword"
DB_SSL_MODE: "disable" # In prod: "require" https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-SSLMODE-STATEMENTS

# API
METABASE_SITE_URL: "https://zengate-global.metabaseapp.com"
METABASE_SECRET_KEY: "bdc8d3f820b8649e7fc2f8d662f515f4e4ae8fa2013d37c008e67a1d7a109802"
ALLOWED_ORIGIN: "http://localhost:9080"

# Auth
FIREBASE_AUTH_SECRET: AIzaSyAyh0U8zJdw9c_YyZzXQkR0Wr7ZuxH7860
FE_URL: "http://localhost:9080"
FIREBASE_ADMIN_KEY_PATH=/serviceAccount.json

# Front end
NEXT_PUBLIC_BACKEND_URL: "http://localhost:4001/"
