{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalDependencies": [".env"], "tasks": {"db:generate": {"cache": false}, "db:migrate": {"cache": false, "persistent": true}, "db:deploy": {"cache": false}, "generate": {"cache": false}, "build": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "dependsOn": ["generate", "^db:generate", "^build"], "outputs": ["build/**", ".vercel/**", "dist/**", ".next/**", "!.next/cache/**"], "cache": false}, "test": {"outputs": ["coverage/**"], "dependsOn": []}, "lint": {"dependsOn": ["^build", "^lint"]}, "check-types": {"dependsOn": ["^build", "^check-types"]}, "dev": {"dependsOn": ["^db:generate", "^build"], "cache": false}, "clean": {"cache": false}}}