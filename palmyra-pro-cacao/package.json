{"name": "palmyra-pro-cacao", "private": true, "author": "zenGate", "version": "0.1.0", "repository": {"type": "git", "url": "git+https://github.com/zenGate-Global/palmyra-pro-cacao.git"}, "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "test": "turbo run test", "check-types": "turbo run check-types"}, "devDependencies": {"@types/gradient-string": "^1.1.6", "prettier": "^3.5.0", "turbo": "^2.5.4"}, "engines": {"node": ">=22"}, "workspaces": ["apps/*", "packages/*"], "packageManager": "npm@10.9.2", "dependencies": {"firebase": "^11.10.0", "firebase-admin": "^13.4.0"}}